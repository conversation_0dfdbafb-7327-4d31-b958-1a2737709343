"use client"

import { useEffect } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Building, Calendar, Info } from 'lucide-react'
import { useFieldBlurAutoSave } from '@/hooks/use-auto-save'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { mouDetailsSchema } from '@/lib/validations/mou-application'
import { demoOrganization } from '@/data/mock-data'

type FormData = z.infer<typeof mouDetailsSchema>

export function NewMouDetailsStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const { data, updateMouDetails } = useMouApplicationStore()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(mouDetailsSchema),
    defaultValues: {
      organizationName: data.organizationName || demoOrganization.name,
      mouDuration: data.mouDuration,
      extendedDurationReason: data.extendedDurationReason || ''
    }
  })

  const mouDuration = watch('mouDuration')
  const showReasonField = mouDuration > 1

  // Update store when form values change
  useEffect(() => {
    const subscription = watch((value) => {
      if (value.mouDuration !== undefined) {
        updateMouDetails(
          value.mouDuration,
          value.extendedDurationReason
        )
      }
    })
    return () => subscription.unsubscribe()
  }, [watch, updateMouDetails])

  return (
    <div className="space-y-6">
      {/* Organization Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Organization Information
          </CardTitle>
          <CardDescription>
            Your organization details for this MoU application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="organizationName">Organization Name</Label>
            <Input
              id="organizationName"
              {...register('organizationName')}
              readOnly
              className="bg-muted cursor-not-allowed"
              placeholder="Organization name will be loaded from your profile"
            />
            <p className="text-sm text-muted-foreground">
              This field is automatically populated from your authenticated partner profile
            </p>
            {errors.organizationName && (
              <p className="text-sm text-destructive">{errors.organizationName.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* MoU Duration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            MoU Duration
          </CardTitle>
          <CardDescription>
            Specify the duration of the Memorandum of Understanding
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="mouDuration">Duration (Years)</Label>
            <Input
              id="mouDuration"
              type="number"
              min="1"
              max="10"
              {...register('mouDuration', { 
                valueAsNumber: true,
                onBlur: onBlur 
              })}
              placeholder="Enter duration in years"
            />
            <p className="text-sm text-muted-foreground">
              Default duration is 1 year. Maximum allowed is 10 years.
            </p>
            {errors.mouDuration && (
              <p className="text-sm text-destructive">{errors.mouDuration.message}</p>
            )}
          </div>

          {/* Conditional Justification Field */}
          {showReasonField && (
            <div className="space-y-2">
              <Label htmlFor="extendedDurationReason">
                Justification for Extended Duration
                <span className="text-destructive ml-1">*</span>
              </Label>
              <Textarea
                id="extendedDurationReason"
                {...register('extendedDurationReason', { onBlur: onBlur })}
                placeholder="Please provide justification for why the MoU duration exceeds 1 year..."
                rows={4}
                className="resize-none"
              />
              <p className="text-sm text-muted-foreground">
                Required when duration is more than 1 year. Explain the rationale for the extended period.
              </p>
              {errors.extendedDurationReason && (
                <p className="text-sm text-destructive">{errors.extendedDurationReason.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Information Alert */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Note:</strong> The organization name is automatically populated from your authenticated 
          partner profile and cannot be modified. If you need to change the organization, please contact 
          the system administrator.
        </AlertDescription>
      </Alert>
    </div>
  )
}
