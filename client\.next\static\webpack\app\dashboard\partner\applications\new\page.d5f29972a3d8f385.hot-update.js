"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./lib/validations/mou-application.ts":
/*!********************************************!*\
  !*** ./lib/validations/mou-application.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   activitiesSchema: () => (/* binding */ activitiesSchema),\n/* harmony export */   activitySchema: () => (/* binding */ activitySchema),\n/* harmony export */   budgetAllocationSchema: () => (/* binding */ budgetAllocationSchema),\n/* harmony export */   documentSchema: () => (/* binding */ documentSchema),\n/* harmony export */   documentsSchema: () => (/* binding */ documentsSchema),\n/* harmony export */   fiscalYearBudgetSchema: () => (/* binding */ fiscalYearBudgetSchema),\n/* harmony export */   mouApplicationSchema: () => (/* binding */ mouApplicationSchema),\n/* harmony export */   mouDetailsSchema: () => (/* binding */ mouDetailsSchema),\n/* harmony export */   partiesSchema: () => (/* binding */ partiesSchema),\n/* harmony export */   partySchema: () => (/* binding */ partySchema),\n/* harmony export */   projectGoalSchema: () => (/* binding */ projectGoalSchema),\n/* harmony export */   projectSchema: () => (/* binding */ projectSchema),\n/* harmony export */   projectsSchema: () => (/* binding */ projectsSchema),\n/* harmony export */   validateProjectBudgets: () => (/* binding */ validateProjectBudgets),\n/* harmony export */   validateStep: () => (/* binding */ validateStep)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/.pnpm/zod@3.25.42/node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./data/mock-data.ts\");\n\n\n// Step 1: MoU Details validation\nconst mouDetailsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    organizationName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Organization name is required'),\n    mouDuration: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(1, 'Duration must be at least 1 year').max(10, 'Duration cannot exceed 10 years'),\n    extendedDurationReason: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n}).refine((data)=>{\n    if (data.mouDuration > 1 && (!data.extendedDurationReason || data.extendedDurationReason.trim() === '')) {\n        return false;\n    }\n    return true;\n}, {\n    message: \"Reason is required when duration is more than 1 year\",\n    path: [\n        \"extendedDurationReason\"\n    ]\n});\n// Step 2: Party Details validation\nconst partySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Party name is required').max(100, 'Party name is too long'),\n    signatoryName: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Signatory name is required').max(100, 'Signatory name is too long'),\n    position: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Position is required').max(100, 'Position is too long'),\n    responsibilities: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional() // Made optional since Party 2+ doesn't need responsibilities\n});\nconst partiesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    parties: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(partySchema).min(1, 'At least one party is required').max(10, 'Maximum 10 parties allowed')\n});\n// Step 3: Projects validation\nconst fiscalYearBudgetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    fiscalYear: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Fiscal year is required'),\n    budget: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Budget must be a positive number').max(1000000000, 'Budget is too large')\n});\nconst projectGoalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    isOverallGoal: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()\n});\nconst projectSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Project name is required').max(200, 'Project name is too long'),\n    fundingSourceId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Funding source is required'),\n    fundingUnitId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Funding unit is required'),\n    budgetTypeId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget type is required'),\n    currencyId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Currency is required'),\n    startDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Start date is required'),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'End date is required'),\n    fiscalYearBudgets: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(fiscalYearBudgetSchema).min(1, 'At least one fiscal year budget is required'),\n    goals: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(projectGoalSchema).min(1, 'At least one goal is required')\n}).refine((data)=>{\n    const startDate = new Date(data.startDate);\n    const endDate = new Date(data.endDate);\n    return startDate < endDate;\n}, {\n    message: \"End date must be after start date\",\n    path: [\n        \"endDate\"\n    ]\n}).refine((data)=>{\n    const overallGoals = data.goals.filter((g)=>g.isOverallGoal);\n    return overallGoals.length === 1;\n}, {\n    message: \"Exactly one overall goal is required\",\n    path: [\n        \"goals\"\n    ]\n});\nconst projectsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    projects: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(projectSchema).min(1, 'At least one project is required').max(20, 'Maximum 20 projects allowed')\n});\n// Step 4: Activities validation\nconst budgetAllocationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    location: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Location is required'),\n    budget: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0, 'Budget must be a positive number')\n});\nconst activitySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    projectId: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Project ID is required'),\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Activity name is required').max(200, 'Activity name is too long'),\n    implementor: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Implementor is required').max(100, 'Implementor name is too long'),\n    implementingUnit: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Implementing unit is required').max(100, 'Implementing unit name is too long'),\n    fiscalYear: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Fiscal year is required'),\n    startDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Start date is required'),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'End date is required'),\n    domain: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Domain is required'),\n    subDomain: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Sub domain is required'),\n    subDomainFunction: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Sub domain function is required'),\n    subFunction: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Sub function is required'),\n    inputCategory: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Input category is required'),\n    activityInput: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Activity input is required'),\n    geographicLevel: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'Provinces',\n        'Central'\n    ], {\n        required_error: 'Geographic level is required'\n    }),\n    budgetAllocations: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(budgetAllocationSchema).min(1, 'At least one budget allocation is required')\n}).refine((data)=>{\n    const startDate = new Date(data.startDate);\n    const endDate = new Date(data.endDate);\n    return startDate < endDate;\n}, {\n    message: \"End date must be after start date\",\n    path: [\n        \"endDate\"\n    ]\n});\nconst activitiesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    activities: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(activitySchema)\n});\n// Step 5: Documents validation\nconst documentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    file: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"instanceof\"](File, {\n        message: 'File is required'\n    }).refine((file)=>file.size <= _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.fileUploadConfig.maxSize, {\n        message: \"File size must be less than \".concat(Math.round(_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.fileUploadConfig.maxSize / (1024 * 1024)), \"MB\")\n    }).refine((file)=>{\n        var _file_name_split_pop;\n        const fileExtension = '.' + ((_file_name_split_pop = file.name.split('.').pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase());\n        return _data_mock_data__WEBPACK_IMPORTED_MODULE_1__.fileUploadConfig.acceptedExtensions.includes(fileExtension);\n    }, {\n        message: \"File type must be one of: \".concat(_data_mock_data__WEBPACK_IMPORTED_MODULE_1__.fileUploadConfig.acceptedExtensions.join(', '))\n    })\n});\nconst documentsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    documents: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        MEMO_OBJECTIVE: documentSchema,\n        STRATEGIC_PLAN: documentSchema,\n        CAPACITY_BUILDING: documentSchema,\n        MEMO_FUNDS: documentSchema\n    })\n});\n// Complete form validation\nconst mouApplicationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    mouDetails: mouDetailsSchema,\n    parties: partiesSchema,\n    projects: projectsSchema,\n    activities: activitiesSchema,\n    documents: documentsSchema\n});\n// Step validation functions\nconst validateStep = (step, data)=>{\n    try {\n        switch(step){\n            case 1:\n                mouDetailsSchema.parse(data);\n                return {\n                    isValid: true,\n                    errors: {}\n                };\n            case 2:\n                // Validate basic party requirements\n                partiesSchema.parse({\n                    parties: data.parties\n                });\n                // Additional validation: Check basic required fields for primary party\n                if (data.parties && data.parties.length > 0) {\n                    var _primaryParty_name, _primaryParty_signatoryName, _primaryParty_position;\n                    const primaryParty = data.parties[0];\n                    // Check if all required basic fields are filled\n                    if (!((_primaryParty_name = primaryParty.name) === null || _primaryParty_name === void 0 ? void 0 : _primaryParty_name.trim())) {\n                        throw new Error(\"Primary party organization name is required\");\n                    }\n                    if (!((_primaryParty_signatoryName = primaryParty.signatoryName) === null || _primaryParty_signatoryName === void 0 ? void 0 : _primaryParty_signatoryName.trim())) {\n                        throw new Error(\"Primary party signatory name is required\");\n                    }\n                    if (!((_primaryParty_position = primaryParty.position) === null || _primaryParty_position === void 0 ? void 0 : _primaryParty_position.trim())) {\n                        throw new Error(\"Primary party position is required\");\n                    }\n                // Responsibilities are optional for progression - can be added later\n                // This allows users to move forward with basic info and add responsibilities later\n                }\n                return {\n                    isValid: true,\n                    errors: {}\n                };\n            case 3:\n                projectsSchema.parse({\n                    projects: data.projects\n                });\n                return {\n                    isValid: true,\n                    errors: {}\n                };\n            case 4:\n                activitiesSchema.parse({\n                    activities: data.activities\n                });\n                return {\n                    isValid: true,\n                    errors: {}\n                };\n            case 5:\n                var _data_documents_find, _data_documents_find1, _data_documents_find2, _data_documents_find3;\n                const documentData = {\n                    documents: {\n                        MEMO_OBJECTIVE: (_data_documents_find = data.documents.find((d)=>d.type === 'MEMO_OBJECTIVE')) === null || _data_documents_find === void 0 ? void 0 : _data_documents_find.file,\n                        STRATEGIC_PLAN: (_data_documents_find1 = data.documents.find((d)=>d.type === 'STRATEGIC_PLAN')) === null || _data_documents_find1 === void 0 ? void 0 : _data_documents_find1.file,\n                        CAPACITY_BUILDING: (_data_documents_find2 = data.documents.find((d)=>d.type === 'CAPACITY_BUILDING')) === null || _data_documents_find2 === void 0 ? void 0 : _data_documents_find2.file,\n                        MEMO_FUNDS: (_data_documents_find3 = data.documents.find((d)=>d.type === 'MEMO_FUNDS')) === null || _data_documents_find3 === void 0 ? void 0 : _data_documents_find3.file\n                    }\n                };\n                documentsSchema.parse(documentData);\n                return {\n                    isValid: true,\n                    errors: {}\n                };\n            default:\n                return {\n                    isValid: false,\n                    errors: {\n                        general: 'Invalid step'\n                    }\n                };\n        }\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_0__.z.ZodError) {\n            const errors = {};\n            error.errors.forEach((err)=>{\n                const path = err.path.join('.');\n                errors[path] = err.message;\n            });\n            return {\n                isValid: false,\n                errors\n            };\n        }\n        return {\n            isValid: false,\n            errors: {\n                general: 'Validation error'\n            }\n        };\n    }\n};\n// Budget validation helpers\nconst validateProjectBudgets = (projects, activities)=>{\n    const errors = {};\n    projects.forEach((project, projectIndex)=>{\n        const projectActivities = activities.filter((a)=>a.projectId === project.id);\n        const totalActivityBudget = projectActivities.reduce((sum, activity)=>{\n            return sum + activity.budgetAllocations.reduce((actSum, allocation)=>actSum + allocation.budget, 0);\n        }, 0);\n        if (totalActivityBudget > project.totalBudget) {\n            errors[\"projects.\".concat(projectIndex, \".budget\")] = 'Total activity budgets exceed project budget';\n        }\n    });\n    return errors;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/validations/mou-application.ts\n"));

/***/ })

});