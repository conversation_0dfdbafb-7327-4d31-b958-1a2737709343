"use client"

import { useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { 
  mockFundingSources, 
  mockFundingUnits, 
  mockBudgetTypes, 
  mockCurrencies,
  documentTypes
} from '@/data/mock-data'
import { 
  Building, 
  Calendar, 
  Users, 
  FolderOpen, 
  Activity, 
  FileText, 
  Edit, 
  CheckCircle,
  AlertTriangle,
  Info
} from 'lucide-react'

interface ReviewStepProps {
  onEditStep?: (step: number) => void
}

export function NewReviewStep({ onEditStep }: ReviewStepProps) {
  const { data } = useMouApplicationStore()

  // Helper functions to get display names
  const getFundingSourceName = useCallback((id: string) => {
    return mockFundingSources.find(s => s.id === id)?.name || 'Unknown'
  }, [])

  const getFundingUnitName = useCallback((id: string) => {
    return mockFundingUnits.find(u => u.id === id)?.name || 'Unknown'
  }, [])

  const getBudgetTypeName = useCallback((id: string) => {
    return mockBudgetTypes.find(t => t.id === id)?.name || 'Unknown'
  }, [])

  const getCurrencyName = useCallback((id: string) => {
    return mockCurrencies.find(c => c.id === id)?.code || 'Unknown'
  }, [])

  const getDocumentTypeName = useCallback((type: string) => {
    return documentTypes.find(d => d.id === type)?.name || 'Unknown Document'
  }, [])

  // Calculate total budget across all projects
  const totalBudget = data.projects.reduce((sum, project) => sum + (project.totalBudget || 0), 0)

  // Check completion status
  const isComplete = data.parties.length >= 1 && 
                    data.projects.length >= 1 && 
                    data.documents.filter(d => d.uploaded).length === 4

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Review & Submit
          </CardTitle>
          <CardDescription>
            Review all information before submitting your MoU application
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Completion Status */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isComplete ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <AlertTriangle className="h-5 w-5 text-amber-600" />
              )}
              <span className="font-medium">
                Application Status: {isComplete ? 'Ready to Submit' : 'Incomplete'}
              </span>
            </div>
            <Badge variant={isComplete ? 'default' : 'secondary'}>
              {isComplete ? 'Complete' : 'Needs Attention'}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Step 1: MoU Details */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              MoU Details
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditStep?.(1)}
              className="flex items-center gap-1"
            >
              <Edit className="h-3 w-3" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Organization</p>
              <p className="font-medium">{data.organizationName}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Duration</p>
              <p className="font-medium">{data.mouDuration} year{data.mouDuration > 1 ? 's' : ''}</p>
            </div>
          </div>
          {data.extendedDurationReason && (
            <div>
              <p className="text-sm font-medium text-muted-foreground">Justification</p>
              <p className="text-sm">{data.extendedDurationReason}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 2: Parties */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Parties ({data.parties.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditStep?.(2)}
              className="flex items-center gap-1"
            >
              <Edit className="h-3 w-3" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {data.parties.length === 0 ? (
            <p className="text-muted-foreground">No parties added</p>
          ) : (
            <div className="space-y-4">
              {data.parties.map((party, index) => (
                <div key={party.id} className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">Party {index + 1}: {party.name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-muted-foreground">Signatory:</span> {party.signatoryName}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Position:</span> {party.position}
                    </div>
                  </div>
                  <div className="mt-2">
                    <span className="text-muted-foreground text-sm">Responsibilities:</span>
                    <p className="text-sm mt-1">{party.responsibilities}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 3: Projects */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5" />
              Projects ({data.projects.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditStep?.(3)}
              className="flex items-center gap-1"
            >
              <Edit className="h-3 w-3" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {data.projects.length === 0 ? (
            <p className="text-muted-foreground">No projects added</p>
          ) : (
            <div className="space-y-4">
              {data.projects.map((project, index) => (
                <div key={project.id} className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-3">{project.name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm mb-3">
                    <div>
                      <span className="text-muted-foreground">Funding Source:</span> {getFundingSourceName(project.fundingSourceId)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Funding Unit:</span> {getFundingUnitName(project.fundingUnitId)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Budget Type:</span> {getBudgetTypeName(project.budgetTypeId)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Currency:</span> {getCurrencyName(project.currencyId)}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span> {project.startDate} to {project.endDate}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Total Budget:</span> 
                      <span className="font-medium ml-1">
                        {(project.totalBudget || 0).toLocaleString()} {getCurrencyName(project.currencyId)}
                      </span>
                    </div>
                  </div>
                  
                  {/* Fiscal Year Budgets */}
                  {project.fiscalYearBudgets && project.fiscalYearBudgets.length > 0 && (
                    <div className="mb-3">
                      <p className="text-sm font-medium text-muted-foreground mb-2">Fiscal Year Budgets:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {project.fiscalYearBudgets.map((fy, fyIndex) => (
                          <div key={fyIndex} className="text-sm">
                            {fy.fiscalYear}: {fy.budget.toLocaleString()} {getCurrencyName(project.currencyId)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Goals */}
                  {project.goals && project.goals.length > 0 && (
                    <div>
                      <p className="text-sm font-medium text-muted-foreground mb-2">Goals:</p>
                      <div className="space-y-1">
                        {project.goals.map((goal, goalIndex) => (
                          <div key={goalIndex} className="text-sm">
                            <span className="font-medium">
                              {goal.isOverallGoal ? 'Overall Goal' : `Goal ${goalIndex + 1}`}:
                            </span>
                            <span className="ml-1">{goal.description}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
              
              {/* Total Budget Summary */}
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Total Budget Across All Projects:</span>
                  <span className="text-lg font-bold text-primary">
                    {totalBudget.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 4: Activities */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Activities ({data.activities.length})
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditStep?.(4)}
              className="flex items-center gap-1"
            >
              <Edit className="h-3 w-3" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {data.activities.length === 0 ? (
            <p className="text-muted-foreground">No activities added</p>
          ) : (
            <div className="space-y-4">
              {data.activities.map((activity, index) => (
                <div key={activity.id} className="p-4 border rounded-lg">
                  <h4 className="font-medium mb-2">{activity.name}</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-muted-foreground">Implementor:</span> {activity.implementor}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Implementing Unit:</span> {activity.implementingUnit}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Fiscal Year:</span> {activity.fiscalYear}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Duration:</span> {activity.startDate} to {activity.endDate}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Geographic Level:</span> {activity.geographicLevel}
                    </div>
                    <div>
                      <span className="text-muted-foreground">Budget Allocations:</span> {activity.budgetAllocations.length}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Step 5: Documents */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Documents ({data.documents.filter(d => d.uploaded).length}/4)
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEditStep?.(5)}
              className="flex items-center gap-1"
            >
              <Edit className="h-3 w-3" />
              Edit
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.documents.map((doc, index) => (
              <div key={doc.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                    {index + 1}
                  </span>
                  <div>
                    <p className="font-medium">{getDocumentTypeName(doc.type)}</p>
                    {doc.file && (
                      <p className="text-sm text-muted-foreground">{doc.file.name}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {doc.uploaded ? (
                    <Badge variant="default" className="bg-green-600">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Uploaded
                    </Badge>
                  ) : (
                    <Badge variant="secondary">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Missing
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Final Validation */}
      {!isComplete && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Application Incomplete:</strong> Please ensure all required fields are completed and all documents are uploaded before submitting.
          </AlertDescription>
        </Alert>
      )}

      {/* Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Before Submitting:</strong> Please review all information carefully. 
          Once submitted, your application will be reviewed by the relevant authorities. 
          You may be contacted for additional information or clarification.
        </AlertDescription>
      </Alert>
    </div>
  )
}
