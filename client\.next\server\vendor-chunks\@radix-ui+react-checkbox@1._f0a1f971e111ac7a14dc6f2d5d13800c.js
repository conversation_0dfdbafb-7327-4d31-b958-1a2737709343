"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-checkbox@1._f0a1f971e111ac7a14dc6f2d5d13800c";
exports.ids = ["vendor-chunks/@radix-ui+react-checkbox@1._f0a1f971e111ac7a14dc6f2d5d13800c"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-checkbox@1._f0a1f971e111ac7a14dc6f2d5d13800c/node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-checkbox@1._f0a1f971e111ac7a14dc6f2d5d13800c/node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_c325a527ee623bb35f115eb421b60f39/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_003328fe74c3632a3c9c5ce511d98a5d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_07f3fde21329b0c35912c2be5d860183/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_9ef3cbcf428948df205b85d70f8b7c4b/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._95c69095de276e5471810d6112434bd7/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._2ac352e659c445f8ff2bfd8f4fda9236/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_e3803c74fa3732ab7d036a7d08888245/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Checkbox,CheckboxIndicator,Indicator,Root,createCheckboxScope auto */ // packages/react/checkbox/src/Checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProvider, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nvar Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...checkboxProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"Checkbox.useComposedRefs[composedRefs]\": (node)=>setButton(node)\n    }[\"Checkbox.useComposedRefs[composedRefs]\"]);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked = false, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked,\n        onChange: onCheckedChange\n    });\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Checkbox.useEffect\": ()=>{\n            const form2 = button?.form;\n            if (form2) {\n                const reset = {\n                    \"Checkbox.useEffect.reset\": ()=>setChecked(initialCheckedStateRef.current)\n                }[\"Checkbox.useEffect.reset\"];\n                form2.addEventListener(\"reset\", reset);\n                return ({\n                    \"Checkbox.useEffect\": ()=>form2.removeEventListener(\"reset\", reset)\n                })[\"Checkbox.useEffect\"];\n            }\n        }\n    }[\"Checkbox.useEffect\"], [\n        button,\n        setChecked\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(CheckboxProvider, {\n        scope: __scopeCheckbox,\n        state: checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"checkbox\",\n                \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...checkboxProps,\n                ref: composedRefs,\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                    if (event.key === \"Enter\") event.preventDefault();\n                }),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>isIndeterminate(prevChecked) ? true : !prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(BubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                },\n                defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked\n            })\n        ]\n    });\n});\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || isIndeterminate(context.state) || context.state === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n            \"data-state\": getState(context.state),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"none\",\n                ...props.style\n            }\n        })\n    });\n});\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BubbleInput = (props)=>{\n    const { control, checked, bubbles = true, defaultChecked, ...inputProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"BubbleInput.useEffect\": ()=>{\n            const input = ref.current;\n            const inputProto = window.HTMLInputElement.prototype;\n            const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n            const setChecked = descriptor.set;\n            if (prevChecked !== checked && setChecked) {\n                const event = new Event(\"click\", {\n                    bubbles\n                });\n                input.indeterminate = isIndeterminate(checked);\n                setChecked.call(input, isIndeterminate(checked) ? false : checked);\n                input.dispatchEvent(event);\n            }\n        }\n    }[\"BubbleInput.useEffect\"], [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        ...inputProps,\n        tabIndex: -1,\n        ref,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n};\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Checkbox;\nvar Indicator = CheckboxIndicator;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-checkbox@1._f0a1f971e111ac7a14dc6f2d5d13800c/node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ })

};
;