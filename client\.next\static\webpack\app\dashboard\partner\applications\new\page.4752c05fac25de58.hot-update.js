"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/new-activities-step.tsx":
/*!*********************************************************!*\
  !*** ./components/wizard-steps/new-activities-step.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewActivitiesStep: () => (/* binding */ NewActivitiesStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/.pnpm/@hookform+resolvers@3.3.4_r_ffdcfe076cdc90c8a3b00ad082065b78/node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-auto-save */ \"(app-pages-browser)/./hooks/use-auto-save.ts\");\n/* harmony import */ var _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/mou-application-store */ \"(app-pages-browser)/./store/mou-application-store.ts\");\n/* harmony import */ var _lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/validations/mou-application */ \"(app-pages-browser)/./lib/validations/mou-application.ts\");\n/* harmony import */ var _hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-dropdown-data */ \"(app-pages-browser)/./hooks/use-dropdown-data.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,Loader2,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ NewActivitiesStep auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewActivitiesStep() {\n    _s();\n    const { onBlur } = (0,_hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave)();\n    const { data, addActivity, removeActivity, updateActivity } = (0,_store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore)();\n    const { data: dropdownData, loading: dropdownLoading, error: dropdownError } = (0,_hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__.useDropdownData)();\n    // Filter fiscal years to only current and next year\n    const filteredFiscalYears = dropdownData.fiscalYears.filter((fy)=>fy.year === '2024-2025' || fy.year === '2025-2026');\n    const { control, register, handleSubmit, watch, setValue, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__.activitiesSchema),\n        defaultValues: {\n            activities: data.activities.length > 0 ? data.activities : []\n        }\n    });\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useFieldArray)({\n        control,\n        name: \"activities\"\n    });\n    const activities = watch('activities');\n    // Get available projects for activity assignment\n    const availableProjects = data.projects.map((project)=>({\n            id: project.id,\n            name: project.name\n        }));\n    // Add new activity\n    const handleAddActivity = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[handleAddActivity]\": ()=>{\n            const newActivity = {\n                projectId: '',\n                name: '',\n                implementor: '',\n                implementingUnit: '',\n                fiscalYear: '',\n                startDate: '',\n                endDate: '',\n                domain: '',\n                subDomain: '',\n                subDomainFunction: '',\n                subFunction: '',\n                inputCategory: '',\n                activityInput: '',\n                geographicLevel: 'Provinces',\n                budgetAllocations: []\n            };\n            append(newActivity);\n            addActivity(newActivity);\n        }\n    }[\"NewActivitiesStep.useCallback[handleAddActivity]\"], [\n        append,\n        addActivity\n    ]);\n    // Remove activity\n    const handleRemoveActivity = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[handleRemoveActivity]\": (index)=>{\n            var _data_activities_index;\n            const activityId = (_data_activities_index = data.activities[index]) === null || _data_activities_index === void 0 ? void 0 : _data_activities_index.id;\n            if (activityId) {\n                removeActivity(activityId);\n            }\n            remove(index);\n        }\n    }[\"NewActivitiesStep.useCallback[handleRemoveActivity]\"], [\n        remove,\n        removeActivity,\n        data.activities\n    ]);\n    // Update activity in store when form changes\n    const handleActivityUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[handleActivityUpdate]\": (index, field, value)=>{\n            var _data_activities_index;\n            const activityId = (_data_activities_index = data.activities[index]) === null || _data_activities_index === void 0 ? void 0 : _data_activities_index.id;\n            if (activityId) {\n                updateActivity(activityId, {\n                    [field]: value\n                });\n            }\n        }\n    }[\"NewActivitiesStep.useCallback[handleActivityUpdate]\"], [\n        updateActivity,\n        data.activities\n    ]);\n    // Get cascading dropdown options\n    const getSubDomains = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getSubDomains]\": (domainId)=>{\n            const domain = dropdownData.domains.find({\n                \"NewActivitiesStep.useCallback[getSubDomains].domain\": (d)=>d.id === domainId\n            }[\"NewActivitiesStep.useCallback[getSubDomains].domain\"]);\n            return (domain === null || domain === void 0 ? void 0 : domain.subDomains) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getSubDomains]\"], [\n        dropdownData.domains\n    ]);\n    const getDomainFunctions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getDomainFunctions]\": (domainId, subDomainId)=>{\n            const domain = dropdownData.domains.find({\n                \"NewActivitiesStep.useCallback[getDomainFunctions].domain\": (d)=>d.id === domainId\n            }[\"NewActivitiesStep.useCallback[getDomainFunctions].domain\"]);\n            const subDomain = domain === null || domain === void 0 ? void 0 : domain.subDomains.find({\n                \"NewActivitiesStep.useCallback[getDomainFunctions]\": (sd)=>sd.id === subDomainId\n            }[\"NewActivitiesStep.useCallback[getDomainFunctions]\"]);\n            return (subDomain === null || subDomain === void 0 ? void 0 : subDomain.functions) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getDomainFunctions]\"], [\n        dropdownData.domains\n    ]);\n    const getSubFunctions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getSubFunctions]\": (domainId, subDomainId, functionId)=>{\n            const domain = dropdownData.domains.find({\n                \"NewActivitiesStep.useCallback[getSubFunctions].domain\": (d)=>d.id === domainId\n            }[\"NewActivitiesStep.useCallback[getSubFunctions].domain\"]);\n            const subDomain = domain === null || domain === void 0 ? void 0 : domain.subDomains.find({\n                \"NewActivitiesStep.useCallback[getSubFunctions]\": (sd)=>sd.id === subDomainId\n            }[\"NewActivitiesStep.useCallback[getSubFunctions]\"]);\n            const domainFunction = subDomain === null || subDomain === void 0 ? void 0 : subDomain.functions.find({\n                \"NewActivitiesStep.useCallback[getSubFunctions]\": (f)=>f.id === functionId\n            }[\"NewActivitiesStep.useCallback[getSubFunctions]\"]);\n            return (domainFunction === null || domainFunction === void 0 ? void 0 : domainFunction.subFunctions) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getSubFunctions]\"], [\n        dropdownData.domains\n    ]);\n    const getActivityInputs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getActivityInputs]\": (categoryId)=>{\n            const category = dropdownData.inputCategories.find({\n                \"NewActivitiesStep.useCallback[getActivityInputs].category\": (c)=>c.id === categoryId\n            }[\"NewActivitiesStep.useCallback[getActivityInputs].category\"]);\n            return (category === null || category === void 0 ? void 0 : category.inputs) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getActivityInputs]\"], [\n        dropdownData.inputCategories\n    ]);\n    const getDistricts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getDistricts]\": (provinceId)=>{\n            const province = dropdownData.provinces.find({\n                \"NewActivitiesStep.useCallback[getDistricts].province\": (p)=>p.id === provinceId\n            }[\"NewActivitiesStep.useCallback[getDistricts].province\"]);\n            return (province === null || province === void 0 ? void 0 : province.districts) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getDistricts]\"], [\n        dropdownData.provinces\n    ]);\n    // Show loading state while fetching dropdown data\n    if (dropdownLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"flex items-center justify-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Loading activity configuration data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 131,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if dropdown data failed to load\n    if (dropdownError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: \"Failed to load activity configuration data. Please refresh the page and try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n            lineNumber: 146,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                \"Activity Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            children: \"Create activities for each project with detailed implementation information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            availableProjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: \"No projects available. Please add at least one project in the previous step before creating activities.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this),\n            availableProjects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: fields.map((field, index)=>{\n                    var _errors_activities_index, _errors_activities, _errors_activities_index_projectId, _errors_activities_index1, _errors_activities_index2, _errors_activities1, _errors_activities_index_name, _errors_activities_index3, _errors_activities_index4, _errors_activities2, _errors_activities_index_implementor, _errors_activities_index5, _errors_activities_index6, _errors_activities3, _errors_activities_index_implementingUnit, _errors_activities_index7, _errors_activities_index8, _errors_activities4, _errors_activities_index_fiscalYear, _errors_activities_index9, _errors_activities_index10, _errors_activities5, _errors_activities_index_startDate, _errors_activities_index11, _errors_activities_index12, _errors_activities6, _errors_activities_index_endDate, _errors_activities_index13, _activities_index, _activities_index1, _activities_index2, _activities_index3, _activities_index4, _activities_index5, _activities_index6, _activities_index7, _activities_index8, _activities_index9, _activities_index10, _activities_index11, _activities_index12, _activities_index_budgetAllocations, _activities_index13;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Activity \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleRemoveActivity(index),\n                                            className: \"text-destructive hover:text-destructive\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Remove\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".projectId\"),\n                                                        children: [\n                                                            \"Assign to Project \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"activities.\".concat(index, \".projectId\"), value);\n                                                            handleActivityUpdate(index, 'projectId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select project\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: availableProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: project.id,\n                                                                        children: project.name\n                                                                    }, project.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities = errors.activities) === null || _errors_activities === void 0 ? void 0 : (_errors_activities_index = _errors_activities[index]) === null || _errors_activities_index === void 0 ? void 0 : _errors_activities_index.projectId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index1 = errors.activities[index]) === null || _errors_activities_index1 === void 0 ? void 0 : (_errors_activities_index_projectId = _errors_activities_index1.projectId) === null || _errors_activities_index_projectId === void 0 ? void 0 : _errors_activities_index_projectId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".name\"),\n                                                        children: [\n                                                            \"Activity Name \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".name\"),\n                                                        ...register(\"activities.\".concat(index, \".name\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'name', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter activity name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities1 = errors.activities) === null || _errors_activities1 === void 0 ? void 0 : (_errors_activities_index2 = _errors_activities1[index]) === null || _errors_activities_index2 === void 0 ? void 0 : _errors_activities_index2.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index3 = errors.activities[index]) === null || _errors_activities_index3 === void 0 ? void 0 : (_errors_activities_index_name = _errors_activities_index3.name) === null || _errors_activities_index_name === void 0 ? void 0 : _errors_activities_index_name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".implementor\"),\n                                                        children: [\n                                                            \"Implementor \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".implementor\"),\n                                                        ...register(\"activities.\".concat(index, \".implementor\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'implementor', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter implementor name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities2 = errors.activities) === null || _errors_activities2 === void 0 ? void 0 : (_errors_activities_index4 = _errors_activities2[index]) === null || _errors_activities_index4 === void 0 ? void 0 : _errors_activities_index4.implementor) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index5 = errors.activities[index]) === null || _errors_activities_index5 === void 0 ? void 0 : (_errors_activities_index_implementor = _errors_activities_index5.implementor) === null || _errors_activities_index_implementor === void 0 ? void 0 : _errors_activities_index_implementor.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".implementingUnit\"),\n                                                        children: [\n                                                            \"Implementing Unit \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".implementingUnit\"),\n                                                        ...register(\"activities.\".concat(index, \".implementingUnit\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'implementingUnit', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter implementing unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities3 = errors.activities) === null || _errors_activities3 === void 0 ? void 0 : (_errors_activities_index6 = _errors_activities3[index]) === null || _errors_activities_index6 === void 0 ? void 0 : _errors_activities_index6.implementingUnit) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index7 = errors.activities[index]) === null || _errors_activities_index7 === void 0 ? void 0 : (_errors_activities_index_implementingUnit = _errors_activities_index7.implementingUnit) === null || _errors_activities_index_implementingUnit === void 0 ? void 0 : _errors_activities_index_implementingUnit.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".fiscalYear\"),\n                                                        children: [\n                                                            \"Fiscal Year \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"activities.\".concat(index, \".fiscalYear\"), value);\n                                                            handleActivityUpdate(index, 'fiscalYear', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select fiscal year\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: filteredFiscalYears.map((fy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: fy.year,\n                                                                        children: fy.year\n                                                                    }, fy.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities4 = errors.activities) === null || _errors_activities4 === void 0 ? void 0 : (_errors_activities_index8 = _errors_activities4[index]) === null || _errors_activities_index8 === void 0 ? void 0 : _errors_activities_index8.fiscalYear) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index9 = errors.activities[index]) === null || _errors_activities_index9 === void 0 ? void 0 : (_errors_activities_index_fiscalYear = _errors_activities_index9.fiscalYear) === null || _errors_activities_index_fiscalYear === void 0 ? void 0 : _errors_activities_index_fiscalYear.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".startDate\"),\n                                                        children: [\n                                                            \"Start Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".startDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"activities.\".concat(index, \".startDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'startDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities5 = errors.activities) === null || _errors_activities5 === void 0 ? void 0 : (_errors_activities_index10 = _errors_activities5[index]) === null || _errors_activities_index10 === void 0 ? void 0 : _errors_activities_index10.startDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index11 = errors.activities[index]) === null || _errors_activities_index11 === void 0 ? void 0 : (_errors_activities_index_startDate = _errors_activities_index11.startDate) === null || _errors_activities_index_startDate === void 0 ? void 0 : _errors_activities_index_startDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".endDate\"),\n                                                        children: [\n                                                            \"End Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 359,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".endDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"activities.\".concat(index, \".endDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'endDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities6 = errors.activities) === null || _errors_activities6 === void 0 ? void 0 : (_errors_activities_index12 = _errors_activities6[index]) === null || _errors_activities_index12 === void 0 ? void 0 : _errors_activities_index12.endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index13 = errors.activities[index]) === null || _errors_activities_index13 === void 0 ? void 0 : (_errors_activities_index_endDate = _errors_activities_index13.endDate) === null || _errors_activities_index_endDate === void 0 ? void 0 : _errors_activities_index_endDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-base font-medium\",\n                                                children: [\n                                                    \"Domain of Intervention \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 44\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".domain\"),\n                                                                children: \"Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".domain\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".subDomain\"), '');\n                                                                    setValue(\"activities.\".concat(index, \".subDomainFunction\"), '');\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), '');\n                                                                    handleActivityUpdate(index, 'domain', value);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select domain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: dropdownData.domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: domain.id,\n                                                                                children: domain.name\n                                                                            }, domain.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 401,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 389,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".subDomain\"),\n                                                                children: \"Sub Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".subDomain\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".subDomainFunction\"), '');\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), '');\n                                                                    handleActivityUpdate(index, 'subDomain', value);\n                                                                },\n                                                                disabled: !((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.domain),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select sub domain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 424,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getSubDomains(((_activities_index1 = activities[index]) === null || _activities_index1 === void 0 ? void 0 : _activities_index1.domain) || '').map((subDomain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: subDomain.id,\n                                                                                children: subDomain.name\n                                                                            }, subDomain.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".subDomainFunction\"),\n                                                                children: \"Sub Domain Function\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".subDomainFunction\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), '');\n                                                                    handleActivityUpdate(index, 'subDomainFunction', value);\n                                                                },\n                                                                disabled: !((_activities_index2 = activities[index]) === null || _activities_index2 === void 0 ? void 0 : _activities_index2.subDomain),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select function\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getDomainFunctions(((_activities_index3 = activities[index]) === null || _activities_index3 === void 0 ? void 0 : _activities_index3.domain) || '', ((_activities_index4 = activities[index]) === null || _activities_index4 === void 0 ? void 0 : _activities_index4.subDomain) || '').map((func)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: func.id,\n                                                                                children: func.name\n                                                                            }, func.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".subFunction\"),\n                                                                children: \"Sub Function\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), value);\n                                                                    handleActivityUpdate(index, 'subFunction', value);\n                                                                },\n                                                                disabled: !((_activities_index5 = activities[index]) === null || _activities_index5 === void 0 ? void 0 : _activities_index5.subDomainFunction),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select sub function\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 473,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getSubFunctions(((_activities_index6 = activities[index]) === null || _activities_index6 === void 0 ? void 0 : _activities_index6.domain) || '', ((_activities_index7 = activities[index]) === null || _activities_index7 === void 0 ? void 0 : _activities_index7.subDomain) || '', ((_activities_index8 = activities[index]) === null || _activities_index8 === void 0 ? void 0 : _activities_index8.subDomainFunction) || '').map((subFunc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: subFunc.id,\n                                                                                children: subFunc.name\n                                                                            }, subFunc.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 476,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-base font-medium\",\n                                                children: [\n                                                    \"Budget Allocation \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".inputCategory\"),\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".inputCategory\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".activityInput\"), '');\n                                                                    handleActivityUpdate(index, 'inputCategory', value);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select category\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: dropdownData.inputCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: category.id,\n                                                                                children: category.name\n                                                                            }, category.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".activityInput\"),\n                                                                children: \"Activity Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".activityInput\"), value);\n                                                                    handleActivityUpdate(index, 'activityInput', value);\n                                                                },\n                                                                disabled: !((_activities_index9 = activities[index]) === null || _activities_index9 === void 0 ? void 0 : _activities_index9.inputCategory),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select activity input\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 533,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 532,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getActivityInputs(((_activities_index10 = activities[index]) === null || _activities_index10 === void 0 ? void 0 : _activities_index10.inputCategory) || '').map((input)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: input.id,\n                                                                                children: input.name\n                                                                            }, input.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 535,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: [\n                                                            \"Location Selection \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".geographicLevel\"),\n                                                        children: \"Geographic Level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"activities.\".concat(index, \".geographicLevel\"), value);\n                                                            setValue(\"activities.\".concat(index, \".budgetAllocations\"), []);\n                                                            handleActivityUpdate(index, 'geographicLevel', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select geographic level\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 567,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"Provinces\",\n                                                                        children: \"Provinces\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 570,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"Central\",\n                                                                        children: \"Central Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 19\n                                            }, this),\n                                            ((_activities_index11 = activities[index]) === null || _activities_index11 === void 0 ? void 0 : _activities_index11.geographicLevel) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Budget Allocations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _activities_index;\n                                                                    const currentAllocations = ((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.budgetAllocations) || [];\n                                                                    const newAllocation = {\n                                                                        location: '',\n                                                                        budget: 0\n                                                                    };\n                                                                    setValue(\"activities.\".concat(index, \".budgetAllocations\"), [\n                                                                        ...currentAllocations,\n                                                                        newAllocation\n                                                                    ]);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Add \",\n                                                                    ((_activities_index12 = activities[index]) === null || _activities_index12 === void 0 ? void 0 : _activities_index12.geographicLevel) === 'Provinces' ? 'Province' : 'Central Level'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    (_activities_index13 = activities[index]) === null || _activities_index13 === void 0 ? void 0 : (_activities_index_budgetAllocations = _activities_index13.budgetAllocations) === null || _activities_index_budgetAllocations === void 0 ? void 0 : _activities_index_budgetAllocations.map((allocation, allocIndex)=>{\n                                                        var _activities_index, _activities_index1, _activities_index2;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: ((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.geographicLevel) === 'Provinces' ? 'Province' : 'Central Level'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 602,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                var _activities_index;\n                                                                                const currentAllocations = [\n                                                                                    ...((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.budgetAllocations) || []\n                                                                                ];\n                                                                                currentAllocations[allocIndex] = {\n                                                                                    ...currentAllocations[allocIndex],\n                                                                                    location: value\n                                                                                };\n                                                                                setValue(\"activities.\".concat(index, \".budgetAllocations\"), currentAllocations);\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                        placeholder: \"Select \".concat(((_activities_index1 = activities[index]) === null || _activities_index1 === void 0 ? void 0 : _activities_index1.geographicLevel) === 'Provinces' ? 'province' : 'central level')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                        lineNumber: 613,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                    lineNumber: 612,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                    children: ((_activities_index2 = activities[index]) === null || _activities_index2 === void 0 ? void 0 : _activities_index2.geographicLevel) === 'Provinces' ? dropdownData.provinces.map((province)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: province.id,\n                                                                                            children: province.name\n                                                                                        }, province.id, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                            lineNumber: 618,\n                                                                                            columnNumber: 39\n                                                                                        }, this)) : dropdownData.centralLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: level.id,\n                                                                                            children: level.name\n                                                                                        }, level.id, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                            lineNumber: 623,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                    lineNumber: 615,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Budget Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 633,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            step: \"0.01\",\n                                                                            ...register(\"activities.\".concat(index, \".budgetAllocations.\").concat(allocIndex, \".budget\"), {\n                                                                                valueAsNumber: true,\n                                                                                onBlur: onBlur\n                                                                            }),\n                                                                            placeholder: \"Enter budget\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 632,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>{\n                                                                            var _activities_index;\n                                                                            const currentAllocations = [\n                                                                                ...((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.budgetAllocations) || []\n                                                                            ];\n                                                                            currentAllocations.splice(allocIndex, 1);\n                                                                            setValue(\"activities.\".concat(index, \".budgetAllocations\"), currentAllocations);\n                                                                        },\n                                                                        className: \"text-destructive hover:text-destructive\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 658,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 646,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, allocIndex, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                            lineNumber: 600,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, field.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 184,\n                columnNumber: 9\n            }, this),\n            availableProjects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-dashed\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: handleAddActivity,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                    lineNumber: 682,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Activity\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 676,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground mt-2\",\n                            children: \"Create activities for your projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                    lineNumber: 675,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 674,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_Loader2_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Note:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, this),\n                            \" Activities represent specific tasks or initiatives within your projects. Ensure budget allocations don't exceed the total project budget.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 695,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 693,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n        lineNumber: 158,\n        columnNumber: 5\n    }, this);\n}\n_s(NewActivitiesStep, \"7g+jCFSe8EquXxI2wEAiZRZJIg8=\", false, function() {\n    return [\n        _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave,\n        _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore,\n        _hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__.useDropdownData,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useFieldArray\n    ];\n});\n_c = NewActivitiesStep;\nvar _c;\n$RefreshReg$(_c, \"NewActivitiesStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/new-activities-step.tsx\n"));

/***/ })

});