import { useState, useEffect, useCallback } from 'react'
import { masterDataService } from '@/lib/services/master-data.service'
import { 
  mockFundingSources, 
  mockFundingUnits, 
  mockBudgetTypes, 
  mockCurrencies,
  mockDomains,
  mockInputCategories,
  mockProvinces,
  mockCentralLevels,
  mockFiscalYears
} from '@/data/mock-data'

// Types for dropdown data
export interface DropdownData {
  fundingSources: Array<{ id: string; name: string }>
  fundingUnits: Array<{ id: string; name: string }>
  budgetTypes: Array<{ id: string; name: string }>
  currencies: Array<{ id: string; code: string; name: string; symbol: string }>
  domains: Array<{
    id: string
    name: string
    subDomains: Array<{
      id: string
      name: string
      functions: Array<{
        id: string
        name: string
        subFunctions: Array<{ id: string; name: string }>
      }>
    }>
  }>
  inputCategories: Array<{
    id: string
    name: string
    inputs: Array<{ id: string; name: string }>
  }>
  provinces: Array<{
    id: string
    name: string
    districts: Array<{ id: string; name: string }>
  }>
  centralLevels: Array<{ id: string; name: string }>
  fiscalYears: Array<{ id: string; year: string }>
}

interface UseDropdownDataOptions {
  enableCaching?: boolean
  fallbackToMock?: boolean
}

interface UseDropdownDataReturn {
  data: DropdownData
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

// Cache for dropdown data
const dataCache = new Map<string, any>()
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

export function useDropdownData(options: UseDropdownDataOptions = {}): UseDropdownDataReturn {
  const { enableCaching = true, fallbackToMock = true } = options
  
  const [data, setData] = useState<DropdownData>({
    fundingSources: mockFundingSources,
    fundingUnits: mockFundingUnits,
    budgetTypes: mockBudgetTypes,
    currencies: mockCurrencies,
    domains: mockDomains,
    inputCategories: mockInputCategories,
    provinces: mockProvinces,
    centralLevels: mockCentralLevels,
    fiscalYears: mockFiscalYears
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Check cache
  const getCachedData = useCallback((key: string) => {
    if (!enableCaching) return null
    
    const cached = dataCache.get(key)
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data
    }
    return null
  }, [enableCaching])

  // Set cache
  const setCachedData = useCallback((key: string, data: any) => {
    if (enableCaching) {
      dataCache.set(key, {
        data,
        timestamp: Date.now()
      })
    }
  }, [enableCaching])

  // Transform API data to match our interface
  const transformFundingSources = useCallback((apiData: any[]) => {
    return apiData.map(item => ({
      id: item.id.toString(),
      name: item.sourceName || item.name
    }))
  }, [])

  const transformFundingUnits = useCallback((apiData: any[]) => {
    return apiData.map(item => ({
      id: item.id.toString(),
      name: item.unitName || item.name
    }))
  }, [])

  const transformBudgetTypes = useCallback((apiData: any[]) => {
    return apiData.map(item => ({
      id: item.id.toString(),
      name: item.typeName || item.name
    }))
  }, [])

  const transformCurrencies = useCallback((apiData: any[]) => {
    return apiData.map(item => ({
      id: item.id.toString(),
      code: item.currencyCode || item.code,
      name: item.currencyName || item.name,
      symbol: item.symbol || '$'
    }))
  }, [])

  // Fetch individual data types
  const fetchFundingSources = useCallback(async () => {
    const cacheKey = 'fundingSources'
    const cached = getCachedData(cacheKey)
    if (cached) return cached

    try {
      const apiData = await masterDataService.getFundingSources()
      const transformed = transformFundingSources(apiData)
      setCachedData(cacheKey, transformed)
      return transformed
    } catch (error) {
      console.warn('Failed to fetch funding sources from API, using mock data:', error)
      if (fallbackToMock) {
        return mockFundingSources
      }
      throw error
    }
  }, [getCachedData, setCachedData, transformFundingSources, fallbackToMock])

  const fetchFundingUnits = useCallback(async () => {
    const cacheKey = 'fundingUnits'
    const cached = getCachedData(cacheKey)
    if (cached) return cached

    try {
      const apiData = await masterDataService.getFundingUnits()
      const transformed = transformFundingUnits(apiData)
      setCachedData(cacheKey, transformed)
      return transformed
    } catch (error) {
      console.warn('Failed to fetch funding units from API, using mock data:', error)
      if (fallbackToMock) {
        return mockFundingUnits
      }
      throw error
    }
  }, [getCachedData, setCachedData, transformFundingUnits, fallbackToMock])

  const fetchBudgetTypes = useCallback(async () => {
    const cacheKey = 'budgetTypes'
    const cached = getCachedData(cacheKey)
    if (cached) return cached

    try {
      const apiData = await masterDataService.getBudgetTypes()
      const transformed = transformBudgetTypes(apiData)
      setCachedData(cacheKey, transformed)
      return transformed
    } catch (error) {
      console.warn('Failed to fetch budget types from API, using mock data:', error)
      if (fallbackToMock) {
        return mockBudgetTypes
      }
      throw error
    }
  }, [getCachedData, setCachedData, transformBudgetTypes, fallbackToMock])

  const fetchCurrencies = useCallback(async () => {
    const cacheKey = 'currencies'
    const cached = getCachedData(cacheKey)
    if (cached) return cached

    try {
      const apiData = await masterDataService.getCurrencies()
      const transformed = transformCurrencies(apiData)
      setCachedData(cacheKey, transformed)
      return transformed
    } catch (error) {
      console.warn('Failed to fetch currencies from API, using mock data:', error)
      if (fallbackToMock) {
        return mockCurrencies
      }
      throw error
    }
  }, [getCachedData, setCachedData, transformCurrencies, fallbackToMock])

  const fetchDomains = useCallback(async () => {
    const cacheKey = 'domains'
    const cached = getCachedData(cacheKey)
    if (cached) return cached

    try {
      const apiData = await masterDataService.getDomainInterventionsTree()
      // Transform API data to match our interface
      // For now, fallback to mock data as the API structure might be different
      setCachedData(cacheKey, mockDomains)
      return mockDomains
    } catch (error) {
      console.warn('Failed to fetch domains from API, using mock data:', error)
      if (fallbackToMock) {
        return mockDomains
      }
      throw error
    }
  }, [getCachedData, setCachedData, fallbackToMock])

  const fetchInputCategories = useCallback(async () => {
    const cacheKey = 'inputCategories'
    const cached = getCachedData(cacheKey)
    if (cached) return cached

    try {
      const apiData = await masterDataService.getInputCategoriesTree()
      // Transform API data to match our interface
      // For now, fallback to mock data as the API structure might be different
      setCachedData(cacheKey, mockInputCategories)
      return mockInputCategories
    } catch (error) {
      console.warn('Failed to fetch input categories from API, using mock data:', error)
      if (fallbackToMock) {
        return mockInputCategories
      }
      throw error
    }
  }, [getCachedData, setCachedData, fallbackToMock])

  // Fetch all data
  const fetchAllData = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const [
        fundingSources,
        fundingUnits,
        budgetTypes,
        currencies,
        domains,
        inputCategories
      ] = await Promise.all([
        fetchFundingSources(),
        fetchFundingUnits(),
        fetchBudgetTypes(),
        fetchCurrencies(),
        fetchDomains(),
        fetchInputCategories()
      ])

      setData({
        fundingSources,
        fundingUnits,
        budgetTypes,
        currencies,
        domains,
        inputCategories,
        provinces: mockProvinces, // Use mock data for now
        centralLevels: mockCentralLevels, // Use mock data for now
        fiscalYears: mockFiscalYears // Use mock data for now
      })
    } catch (error) {
      console.error('Failed to fetch dropdown data:', error)
      setError('Failed to load dropdown data')
      
      // Fallback to mock data
      if (fallbackToMock) {
        setData({
          fundingSources: mockFundingSources,
          fundingUnits: mockFundingUnits,
          budgetTypes: mockBudgetTypes,
          currencies: mockCurrencies,
          domains: mockDomains,
          inputCategories: mockInputCategories,
          provinces: mockProvinces,
          centralLevels: mockCentralLevels,
          fiscalYears: mockFiscalYears
        })
      }
    } finally {
      setLoading(false)
    }
  }, [
    fetchFundingSources,
    fetchFundingUnits,
    fetchBudgetTypes,
    fetchCurrencies,
    fetchDomains,
    fetchInputCategories,
    fallbackToMock
  ])

  // Initial data fetch
  useEffect(() => {
    fetchAllData()
  }, [fetchAllData])

  return {
    data,
    loading,
    error,
    refetch: fetchAllData
  }
}
