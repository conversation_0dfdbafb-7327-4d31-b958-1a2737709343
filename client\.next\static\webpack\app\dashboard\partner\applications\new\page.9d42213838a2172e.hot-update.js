"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/new-projects-step.tsx":
/*!*******************************************************!*\
  !*** ./components/wizard-steps/new-projects-step.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewProjectsStep: () => (/* binding */ NewProjectsStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/.pnpm/@hookform+resolvers@3.3.4_r_ffdcfe076cdc90c8a3b00ad082065b78/node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-auto-save */ \"(app-pages-browser)/./hooks/use-auto-save.ts\");\n/* harmony import */ var _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/mou-application-store */ \"(app-pages-browser)/./store/mou-application-store.ts\");\n/* harmony import */ var _lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/validations/mou-application */ \"(app-pages-browser)/./lib/validations/mou-application.ts\");\n/* harmony import */ var _hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-dropdown-data */ \"(app-pages-browser)/./hooks/use-dropdown-data.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ NewProjectsStep auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewProjectsStep() {\n    _s();\n    const { onBlur } = (0,_hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave)();\n    const { data, addProject, removeProject, updateProject } = (0,_store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore)();\n    const { data: dropdownData, loading: dropdownLoading, error: dropdownError } = (0,_hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__.useDropdownData)();\n    const { control, register, handleSubmit, watch, setValue, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__.projectsSchema),\n        defaultValues: {\n            projects: data.projects.length > 0 ? data.projects : []\n        }\n    });\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useFieldArray)({\n        control,\n        name: \"projects\"\n    });\n    const projects = watch('projects');\n    // Add new project\n    const handleAddProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[handleAddProject]\": ()=>{\n            const newProject = {\n                name: '',\n                fundingSourceId: '',\n                fundingUnitId: '',\n                budgetTypeId: '',\n                currencyId: '',\n                startDate: '',\n                endDate: '',\n                fiscalYearBudgets: [\n                    {\n                        fiscalYear: '2024-2025',\n                        budget: 0\n                    }\n                ],\n                goals: [\n                    {\n                        description: '',\n                        isOverallGoal: true\n                    }\n                ]\n            };\n            append(newProject);\n            addProject(newProject);\n        }\n    }[\"NewProjectsStep.useCallback[handleAddProject]\"], [\n        append,\n        addProject\n    ]);\n    // Remove project\n    const handleRemoveProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[handleRemoveProject]\": (index)=>{\n            var _data_projects_index;\n            const projectId = (_data_projects_index = data.projects[index]) === null || _data_projects_index === void 0 ? void 0 : _data_projects_index.id;\n            if (projectId) {\n                removeProject(projectId);\n            }\n            remove(index);\n        }\n    }[\"NewProjectsStep.useCallback[handleRemoveProject]\"], [\n        remove,\n        removeProject,\n        data.projects\n    ]);\n    // Calculate total budget for a project\n    const calculateTotalBudget = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[calculateTotalBudget]\": (projectIndex)=>{\n            const project = projects[projectIndex];\n            if (!(project === null || project === void 0 ? void 0 : project.fiscalYearBudgets)) return 0;\n            return project.fiscalYearBudgets.reduce({\n                \"NewProjectsStep.useCallback[calculateTotalBudget]\": (sum, fy)=>sum + (fy.budget || 0)\n            }[\"NewProjectsStep.useCallback[calculateTotalBudget]\"], 0);\n        }\n    }[\"NewProjectsStep.useCallback[calculateTotalBudget]\"], [\n        projects\n    ]);\n    // Update project in store when form changes\n    const handleProjectUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[handleProjectUpdate]\": (index, field, value)=>{\n            var _data_projects_index;\n            const projectId = (_data_projects_index = data.projects[index]) === null || _data_projects_index === void 0 ? void 0 : _data_projects_index.id;\n            if (projectId) {\n                updateProject(projectId, {\n                    [field]: value\n                });\n            }\n        }\n    }[\"NewProjectsStep.useCallback[handleProjectUpdate]\"], [\n        updateProject,\n        data.projects\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this),\n                                \"Project Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            children: \"Define projects that will be covered under this MoU\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: fields.map((field, index)=>{\n                    var _errors_projects_index, _errors_projects, _errors_projects_index_name, _errors_projects_index1, _errors_projects_index2, _errors_projects1, _errors_projects_index_fundingSourceId, _errors_projects_index3, _errors_projects_index4, _errors_projects2, _errors_projects_index_fundingUnitId, _errors_projects_index5, _errors_projects_index6, _errors_projects3, _errors_projects_index_budgetTypeId, _errors_projects_index7, _errors_projects_index8, _errors_projects4, _errors_projects_index_currencyId, _errors_projects_index9, _errors_projects_index10, _errors_projects5, _errors_projects_index_startDate, _errors_projects_index11, _errors_projects_index12, _errors_projects6, _errors_projects_index_endDate, _errors_projects_index13, _projects_index_fiscalYearBudgets, _projects_index, _projects_index_goals, _projects_index1, _projects_index2, _dropdownData_currencies_find;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Project \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleRemoveProject(index),\n                                            className: \"text-destructive hover:text-destructive\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Remove\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".name\"),\n                                                        children: [\n                                                            \"Project Name \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"projects.\".concat(index, \".name\"),\n                                                        ...register(\"projects.\".concat(index, \".name\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleProjectUpdate(index, 'name', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter project name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects = errors.projects) === null || _errors_projects === void 0 ? void 0 : (_errors_projects_index = _errors_projects[index]) === null || _errors_projects_index === void 0 ? void 0 : _errors_projects_index.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index1 = errors.projects[index]) === null || _errors_projects_index1 === void 0 ? void 0 : (_errors_projects_index_name = _errors_projects_index1.name) === null || _errors_projects_index_name === void 0 ? void 0 : _errors_projects_index_name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".fundingSourceId\"),\n                                                        children: [\n                                                            \"Funding Source \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 162,\n                                                                columnNumber: 36\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".fundingSourceId\"), value);\n                                                            handleProjectUpdate(index, 'fundingSourceId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select funding source\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.fundingSources.map((source)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: source.id,\n                                                                        children: source.name\n                                                                    }, source.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects1 = errors.projects) === null || _errors_projects1 === void 0 ? void 0 : (_errors_projects_index2 = _errors_projects1[index]) === null || _errors_projects_index2 === void 0 ? void 0 : _errors_projects_index2.fundingSourceId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index3 = errors.projects[index]) === null || _errors_projects_index3 === void 0 ? void 0 : (_errors_projects_index_fundingSourceId = _errors_projects_index3.fundingSourceId) === null || _errors_projects_index_fundingSourceId === void 0 ? void 0 : _errors_projects_index_fundingSourceId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".fundingUnitId\"),\n                                                        children: [\n                                                            \"Funding Unit \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".fundingUnitId\"), value);\n                                                            handleProjectUpdate(index, 'fundingUnitId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select funding unit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.fundingUnits.map((unit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: unit.id,\n                                                                        children: unit.name\n                                                                    }, unit.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects2 = errors.projects) === null || _errors_projects2 === void 0 ? void 0 : (_errors_projects_index4 = _errors_projects2[index]) === null || _errors_projects_index4 === void 0 ? void 0 : _errors_projects_index4.fundingUnitId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index5 = errors.projects[index]) === null || _errors_projects_index5 === void 0 ? void 0 : (_errors_projects_index_fundingUnitId = _errors_projects_index5.fundingUnitId) === null || _errors_projects_index_fundingUnitId === void 0 ? void 0 : _errors_projects_index_fundingUnitId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".budgetTypeId\"),\n                                                        children: [\n                                                            \"Budget Type \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".budgetTypeId\"), value);\n                                                            handleProjectUpdate(index, 'budgetTypeId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select budget type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.budgetTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: type.id,\n                                                                        children: type.name\n                                                                    }, type.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 233,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects3 = errors.projects) === null || _errors_projects3 === void 0 ? void 0 : (_errors_projects_index6 = _errors_projects3[index]) === null || _errors_projects_index6 === void 0 ? void 0 : _errors_projects_index6.budgetTypeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index7 = errors.projects[index]) === null || _errors_projects_index7 === void 0 ? void 0 : (_errors_projects_index_budgetTypeId = _errors_projects_index7.budgetTypeId) === null || _errors_projects_index_budgetTypeId === void 0 ? void 0 : _errors_projects_index_budgetTypeId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".currencyId\"),\n                                                        children: [\n                                                            \"Currency \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 30\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".currencyId\"), value);\n                                                            handleProjectUpdate(index, 'currencyId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select currency\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 258,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: currency.id,\n                                                                        children: [\n                                                                            currency.code,\n                                                                            \" - \",\n                                                                            currency.name\n                                                                        ]\n                                                                    }, currency.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects4 = errors.projects) === null || _errors_projects4 === void 0 ? void 0 : (_errors_projects_index8 = _errors_projects4[index]) === null || _errors_projects_index8 === void 0 ? void 0 : _errors_projects_index8.currencyId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index9 = errors.projects[index]) === null || _errors_projects_index9 === void 0 ? void 0 : (_errors_projects_index_currencyId = _errors_projects_index9.currencyId) === null || _errors_projects_index_currencyId === void 0 ? void 0 : _errors_projects_index_currencyId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".startDate\"),\n                                                        children: [\n                                                            \"Start Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"projects.\".concat(index, \".startDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"projects.\".concat(index, \".startDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleProjectUpdate(index, 'startDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects5 = errors.projects) === null || _errors_projects5 === void 0 ? void 0 : (_errors_projects_index10 = _errors_projects5[index]) === null || _errors_projects_index10 === void 0 ? void 0 : _errors_projects_index10.startDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index11 = errors.projects[index]) === null || _errors_projects_index11 === void 0 ? void 0 : (_errors_projects_index_startDate = _errors_projects_index11.startDate) === null || _errors_projects_index_startDate === void 0 ? void 0 : _errors_projects_index_startDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".endDate\"),\n                                                        children: [\n                                                            \"End Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 30\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"projects.\".concat(index, \".endDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"projects.\".concat(index, \".endDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleProjectUpdate(index, 'endDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects6 = errors.projects) === null || _errors_projects6 === void 0 ? void 0 : (_errors_projects_index12 = _errors_projects6[index]) === null || _errors_projects_index12 === void 0 ? void 0 : _errors_projects_index12.endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index13 = errors.projects[index]) === null || _errors_projects_index13 === void 0 ? void 0 : (_errors_projects_index_endDate = _errors_projects_index13.endDate) === null || _errors_projects_index_endDate === void 0 ? void 0 : _errors_projects_index_endDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: [\n                                                            \"Fiscal Year Budgets \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _projects_index;\n                                                            const currentBudgets = ((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.fiscalYearBudgets) || [];\n                                                            const newBudget = {\n                                                                fiscalYear: '2025-2026',\n                                                                budget: 0\n                                                            };\n                                                            setValue(\"projects.\".concat(index, \".fiscalYearBudgets\"), [\n                                                                ...currentBudgets,\n                                                                newBudget\n                                                            ]);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Fiscal Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            (_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : (_projects_index_fiscalYearBudgets = _projects_index.fiscalYearBudgets) === null || _projects_index_fiscalYearBudgets === void 0 ? void 0 : _projects_index_fiscalYearBudgets.map((fyBudget, fyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Fiscal Year\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                    onValueChange: (value)=>{\n                                                                        var _projects_index;\n                                                                        const currentBudgets = [\n                                                                            ...((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.fiscalYearBudgets) || []\n                                                                        ];\n                                                                        currentBudgets[fyIndex] = {\n                                                                            ...currentBudgets[fyIndex],\n                                                                            fiscalYear: value\n                                                                        };\n                                                                        setValue(\"projects.\".concat(index, \".fiscalYearBudgets\"), currentBudgets);\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                placeholder: \"Select fiscal year\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                                lineNumber: 357,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                            children: dropdownData.fiscalYears.map((fy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                    value: fy.year,\n                                                                                    children: fy.year\n                                                                                }, fy.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                                    lineNumber: 361,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Budget Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    ...register(\"projects.\".concat(index, \".fiscalYearBudgets.\").concat(fyIndex, \".budget\"), {\n                                                                        valueAsNumber: true,\n                                                                        onBlur: onBlur\n                                                                    }),\n                                                                    placeholder: \"Enter budget amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 371,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _projects_index;\n                                                                    const currentBudgets = [\n                                                                        ...((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.fiscalYearBudgets) || []\n                                                                    ];\n                                                                    currentBudgets.splice(fyIndex, 1);\n                                                                    setValue(\"projects.\".concat(index, \".fiscalYearBudgets\"), currentBudgets);\n                                                                },\n                                                                className: \"text-destructive hover:text-destructive\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, fyIndex, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: [\n                                                            \"Project Goals \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _projects_index;\n                                                            const currentGoals = ((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.goals) || [];\n                                                            const newGoal = {\n                                                                description: '',\n                                                                isOverallGoal: false\n                                                            };\n                                                            setValue(\"projects.\".concat(index, \".goals\"), [\n                                                                ...currentGoals,\n                                                                newGoal\n                                                            ]);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Goal\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this),\n                                            (_projects_index1 = projects[index]) === null || _projects_index1 === void 0 ? void 0 : (_projects_index_goals = _projects_index1.goals) === null || _projects_index_goals === void 0 ? void 0 : _projects_index_goals.map((goal, goalIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: goal.isOverallGoal ? 'Overall Goal' : \"Additional Goal \".concat(goalIndex)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                !goal.isOverallGoal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>{\n                                                                        var _projects_index;\n                                                                        const currentGoals = [\n                                                                            ...((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.goals) || []\n                                                                        ];\n                                                                        currentGoals.splice(goalIndex, 1);\n                                                                        setValue(\"projects.\".concat(index, \".goals\"), currentGoals);\n                                                                    },\n                                                                    className: \"text-destructive hover:text-destructive\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 444,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 428,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            ...register(\"projects.\".concat(index, \".goals.\").concat(goalIndex, \".description\"), {\n                                                                onBlur: onBlur\n                                                            }),\n                                                            placeholder: goal.isOverallGoal ? \"Enter the overall goal for this project...\" : \"Enter additional goal...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, goalIndex, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Project Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-primary\",\n                                                children: [\n                                                    calculateTotalBudget(index).toLocaleString(),\n                                                    ((_projects_index2 = projects[index]) === null || _projects_index2 === void 0 ? void 0 : _projects_index2.currencyId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-normal text-muted-foreground ml-2\",\n                                                        children: (_dropdownData_currencies_find = dropdownData.currencies.find((c)=>{\n                                                            var _projects_index;\n                                                            return c.id === ((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.currencyId);\n                                                        })) === null || _dropdownData_currencies_find === void 0 ? void 0 : _dropdownData_currencies_find.code\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Auto-calculated from fiscal year budgets above\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, field.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-dashed\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: handleAddProject,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                \"Add Project\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground mt-2\",\n                            children: \"Add multiple projects that will be covered under this MoU\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                    lineNumber: 483,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 482,\n                columnNumber: 7\n            }, this),\n            fields.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: \"At least one project must be added to proceed to the next step.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 501,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Note:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this),\n                            \" Each project should represent a distinct initiative or program that will be implemented under this MoU. Ensure all financial and timeline information is accurate.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProjectsStep, \"XIhJpzVeQUPp1d+YcyrP7sX+O+s=\", false, function() {\n    return [\n        _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave,\n        _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore,\n        _hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__.useDropdownData,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useFieldArray\n    ];\n});\n_c = NewProjectsStep;\nvar _c;\n$RefreshReg$(_c, \"NewProjectsStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/new-projects-step.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/use-dropdown-data.ts":
/*!************************************!*\
  !*** ./hooks/use-dropdown-data.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDropdownData: () => (/* binding */ useDropdownData)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/services/master-data.service */ \"(app-pages-browser)/./lib/services/master-data.service.ts\");\n/* harmony import */ var _data_mock_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/data/mock-data */ \"(app-pages-browser)/./data/mock-data.ts\");\n\n\n\n// Cache for dropdown data\nconst dataCache = new Map();\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n;\nfunction useDropdownData() {\n    let options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { enableCaching = true, fallbackToMock = true } = options;\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        fundingSources: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFundingSources,\n        fundingUnits: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFundingUnits,\n        budgetTypes: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockBudgetTypes,\n        currencies: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCurrencies,\n        domains: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockDomains,\n        inputCategories: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockInputCategories,\n        provinces: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockProvinces,\n        centralLevels: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCentralLevels,\n        fiscalYears: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFiscalYears\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Check cache\n    const getCachedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[getCachedData]\": (key)=>{\n            if (!enableCaching) return null;\n            const cached = dataCache.get(key);\n            if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\n                return cached.data;\n            }\n            return null;\n        }\n    }[\"useDropdownData.useCallback[getCachedData]\"], [\n        enableCaching\n    ]);\n    // Set cache\n    const setCachedData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[setCachedData]\": (key, data)=>{\n            if (enableCaching) {\n                dataCache.set(key, {\n                    data,\n                    timestamp: Date.now()\n                });\n            }\n        }\n    }[\"useDropdownData.useCallback[setCachedData]\"], [\n        enableCaching\n    ]);\n    // Transform API data to match our interface\n    const transformFundingSources = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[transformFundingSources]\": (apiData)=>{\n            return apiData.map({\n                \"useDropdownData.useCallback[transformFundingSources]\": (item)=>({\n                        id: item.id.toString(),\n                        name: item.sourceName || item.name\n                    })\n            }[\"useDropdownData.useCallback[transformFundingSources]\"]);\n        }\n    }[\"useDropdownData.useCallback[transformFundingSources]\"], []);\n    const transformFundingUnits = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[transformFundingUnits]\": (apiData)=>{\n            return apiData.map({\n                \"useDropdownData.useCallback[transformFundingUnits]\": (item)=>({\n                        id: item.id.toString(),\n                        name: item.unitName || item.name\n                    })\n            }[\"useDropdownData.useCallback[transformFundingUnits]\"]);\n        }\n    }[\"useDropdownData.useCallback[transformFundingUnits]\"], []);\n    const transformBudgetTypes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[transformBudgetTypes]\": (apiData)=>{\n            return apiData.map({\n                \"useDropdownData.useCallback[transformBudgetTypes]\": (item)=>({\n                        id: item.id.toString(),\n                        name: item.typeName || item.name\n                    })\n            }[\"useDropdownData.useCallback[transformBudgetTypes]\"]);\n        }\n    }[\"useDropdownData.useCallback[transformBudgetTypes]\"], []);\n    const transformCurrencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[transformCurrencies]\": (apiData)=>{\n            return apiData.map({\n                \"useDropdownData.useCallback[transformCurrencies]\": (item)=>({\n                        id: item.id.toString(),\n                        code: item.currencyCode || item.code,\n                        name: item.currencyName || item.name,\n                        symbol: item.symbol || '$'\n                    })\n            }[\"useDropdownData.useCallback[transformCurrencies]\"]);\n        }\n    }[\"useDropdownData.useCallback[transformCurrencies]\"], []);\n    // Fetch individual data types\n    const fetchFundingSources = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchFundingSources]\": async ()=>{\n            const cacheKey = 'fundingSources';\n            const cached = getCachedData(cacheKey);\n            if (cached) return cached;\n            try {\n                const apiData = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__.masterDataService.getFundingSources();\n                const transformed = transformFundingSources(apiData);\n                setCachedData(cacheKey, transformed);\n                return transformed;\n            } catch (error) {\n                console.warn('Failed to fetch funding sources from API, using mock data:', error);\n                if (fallbackToMock) {\n                    return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFundingSources;\n                }\n                throw error;\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchFundingSources]\"], [\n        getCachedData,\n        setCachedData,\n        transformFundingSources,\n        fallbackToMock\n    ]);\n    const fetchFundingUnits = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchFundingUnits]\": async ()=>{\n            const cacheKey = 'fundingUnits';\n            const cached = getCachedData(cacheKey);\n            if (cached) return cached;\n            try {\n                const apiData = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__.masterDataService.getFundingUnits();\n                const transformed = transformFundingUnits(apiData);\n                setCachedData(cacheKey, transformed);\n                return transformed;\n            } catch (error) {\n                console.warn('Failed to fetch funding units from API, using mock data:', error);\n                if (fallbackToMock) {\n                    return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFundingUnits;\n                }\n                throw error;\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchFundingUnits]\"], [\n        getCachedData,\n        setCachedData,\n        transformFundingUnits,\n        fallbackToMock\n    ]);\n    const fetchBudgetTypes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchBudgetTypes]\": async ()=>{\n            const cacheKey = 'budgetTypes';\n            const cached = getCachedData(cacheKey);\n            if (cached) return cached;\n            try {\n                const apiData = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__.masterDataService.getBudgetTypes();\n                const transformed = transformBudgetTypes(apiData);\n                setCachedData(cacheKey, transformed);\n                return transformed;\n            } catch (error) {\n                console.warn('Failed to fetch budget types from API, using mock data:', error);\n                if (fallbackToMock) {\n                    return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockBudgetTypes;\n                }\n                throw error;\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchBudgetTypes]\"], [\n        getCachedData,\n        setCachedData,\n        transformBudgetTypes,\n        fallbackToMock\n    ]);\n    const fetchCurrencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchCurrencies]\": async ()=>{\n            const cacheKey = 'currencies';\n            const cached = getCachedData(cacheKey);\n            if (cached) return cached;\n            try {\n                const apiData = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__.masterDataService.getCurrencies();\n                const transformed = transformCurrencies(apiData);\n                setCachedData(cacheKey, transformed);\n                return transformed;\n            } catch (error) {\n                console.warn('Failed to fetch currencies from API, using mock data:', error);\n                if (fallbackToMock) {\n                    return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCurrencies;\n                }\n                throw error;\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchCurrencies]\"], [\n        getCachedData,\n        setCachedData,\n        transformCurrencies,\n        fallbackToMock\n    ]);\n    const fetchDomains = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchDomains]\": async ()=>{\n            const cacheKey = 'domains';\n            const cached = getCachedData(cacheKey);\n            if (cached) return cached;\n            try {\n                const apiData = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__.masterDataService.getDomainInterventionsTree();\n                // Transform API data to match our interface\n                // For now, fallback to mock data as the API structure might be different\n                setCachedData(cacheKey, _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockDomains);\n                return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockDomains;\n            } catch (error) {\n                console.warn('Failed to fetch domains from API, using mock data:', error);\n                if (fallbackToMock) {\n                    return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockDomains;\n                }\n                throw error;\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchDomains]\"], [\n        getCachedData,\n        setCachedData,\n        fallbackToMock\n    ]);\n    const fetchInputCategories = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchInputCategories]\": async ()=>{\n            const cacheKey = 'inputCategories';\n            const cached = getCachedData(cacheKey);\n            if (cached) return cached;\n            try {\n                const apiData = await _lib_services_master_data_service__WEBPACK_IMPORTED_MODULE_1__.masterDataService.getInputCategoriesTree();\n                // Transform API data to match our interface\n                // For now, fallback to mock data as the API structure might be different\n                setCachedData(cacheKey, _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockInputCategories);\n                return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockInputCategories;\n            } catch (error) {\n                console.warn('Failed to fetch input categories from API, using mock data:', error);\n                if (fallbackToMock) {\n                    return _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockInputCategories;\n                }\n                throw error;\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchInputCategories]\"], [\n        getCachedData,\n        setCachedData,\n        fallbackToMock\n    ]);\n    // Fetch all data\n    const fetchAllData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDropdownData.useCallback[fetchAllData]\": async ()=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const [fundingSources, fundingUnits, budgetTypes, currencies, domains, inputCategories] = await Promise.all([\n                    fetchFundingSources(),\n                    fetchFundingUnits(),\n                    fetchBudgetTypes(),\n                    fetchCurrencies(),\n                    fetchDomains(),\n                    fetchInputCategories()\n                ]);\n                setData({\n                    fundingSources,\n                    fundingUnits,\n                    budgetTypes,\n                    currencies,\n                    domains,\n                    inputCategories,\n                    provinces: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockProvinces,\n                    centralLevels: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCentralLevels,\n                    fiscalYears: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFiscalYears // Use mock data for now\n                });\n            } catch (error) {\n                console.error('Failed to fetch dropdown data:', error);\n                setError('Failed to load dropdown data');\n                // Fallback to mock data\n                if (fallbackToMock) {\n                    setData({\n                        fundingSources: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFundingSources,\n                        fundingUnits: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFundingUnits,\n                        budgetTypes: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockBudgetTypes,\n                        currencies: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCurrencies,\n                        domains: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockDomains,\n                        inputCategories: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockInputCategories,\n                        provinces: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockProvinces,\n                        centralLevels: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockCentralLevels,\n                        fiscalYears: _data_mock_data__WEBPACK_IMPORTED_MODULE_2__.mockFiscalYears\n                    });\n                }\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useDropdownData.useCallback[fetchAllData]\"], [\n        fetchFundingSources,\n        fetchFundingUnits,\n        fetchBudgetTypes,\n        fetchCurrencies,\n        fetchDomains,\n        fetchInputCategories,\n        fallbackToMock\n    ]);\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDropdownData.useEffect\": ()=>{\n            fetchAllData();\n        }\n    }[\"useDropdownData.useEffect\"], [\n        fetchAllData\n    ]);\n    return {\n        data,\n        loading,\n        error,\n        refetch: fetchAllData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-dropdown-data.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/services/master-data.service.ts":
/*!*********************************************!*\
  !*** ./lib/services/master-data.service.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   masterDataService: () => (/* binding */ masterDataService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../api */ \"(app-pages-browser)/./lib/api.ts\");\n\nconst masterDataService = {\n    // Budget Types\n    async getBudgetTypes () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/budget-types\");\n        return response.data;\n    },\n    async createBudgetType (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/budget-types\", data);\n        return response.data;\n    },\n    async updateBudgetType (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/budget-types/\".concat(id), data);\n        return response.data;\n    },\n    async deleteBudgetType (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/budget-types/\".concat(id));\n    },\n    // Funding Sources\n    async getFundingSources () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/funding-sources\");\n        return response.data;\n    },\n    async createFundingSource (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/funding-sources\", data);\n        return response.data;\n    },\n    async updateFundingSource (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/funding-sources/\".concat(id), data);\n        return response.data;\n    },\n    async deleteFundingSource (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/funding-sources/\".concat(id));\n    },\n    // Funding Units\n    async getFundingUnits () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/funding-units\");\n        return response.data;\n    },\n    async createFundingUnit (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/funding-units\", data);\n        return response.data;\n    },\n    async updateFundingUnit (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/funding-units/\".concat(id), data);\n        return response.data;\n    },\n    async deleteFundingUnit (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/funding-units/\".concat(id));\n    },\n    // Organization Types\n    async getOrganizationTypes () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/organization-types\");\n        return response.data;\n    },\n    async createOrganizationType (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/organization-types\", data);\n        return response.data;\n    },\n    async updateOrganizationType (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/organization-types/\".concat(id), data);\n        return response.data;\n    },\n    async deleteOrganizationType (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/organization-types/\".concat(id));\n    },\n    // Health Care Providers\n    async getHealthCareProviders () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/health-care-providers\");\n        return response.data;\n    },\n    async createHealthCareProvider (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/health-care-providers\", data);\n        return response.data;\n    },\n    async updateHealthCareProvider (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/health-care-providers/\".concat(id), data);\n        return response.data;\n    },\n    async deleteHealthCareProvider (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/health-care-providers/\".concat(id));\n    },\n    // Financing Agents\n    async getFinancingAgents () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/financing-agents\");\n        return response.data;\n    },\n    async createFinancingAgent (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/financing-agents\", data);\n        return response.data;\n    },\n    async updateFinancingAgent (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/financing-agents/\".concat(id), data);\n        return response.data;\n    },\n    async deleteFinancingAgent (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/financing-agents/\".concat(id));\n    },\n    // Financing Schemes\n    async getFinancingSchemes () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/financing-schemes\");\n        return response.data;\n    },\n    async createFinancingScheme (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/financing-schemes\", data);\n        return response.data;\n    },\n    async updateFinancingScheme (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/financing-schemes/\".concat(id), data);\n        return response.data;\n    },\n    async deleteFinancingScheme (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/financing-schemes/\".concat(id));\n    },\n    // Input Categories (with hierarchy support)\n    async getInputCategories () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/input-categories\");\n        return response.data;\n    },\n    async getInputCategoriesTree () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/input-categories/tree\");\n        return response.data;\n    },\n    async createInputCategory (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/input-categories\", data);\n        return response.data;\n    },\n    async updateInputCategory (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/input-categories/\".concat(id), data);\n        return response.data;\n    },\n    async deleteInputCategory (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/input-categories/\".concat(id));\n    },\n    // Domain Interventions (with hierarchy support)\n    async getDomainInterventions () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domain-interventions\");\n        return response.data;\n    },\n    async getDomainInterventionsTree () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domain-interventions/tree\");\n        return response.data;\n    },\n    async createDomainIntervention (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domain-interventions\", data);\n        return response.data;\n    },\n    async updateDomainIntervention (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/domain-interventions/\".concat(id), data);\n        return response.data;\n    },\n    async deleteDomainIntervention (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/domain-interventions/\".concat(id));\n    },\n    // Currency\n    async getCurrencies () {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/currencies\");\n        return response.data;\n    },\n    async createCurrency (data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/currencies\", data);\n        return response.data;\n    },\n    async updateCurrency (id, data) {\n        const response = await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/currencies/\".concat(id), data);\n        return response.data;\n    },\n    async deleteCurrency (id) {\n        await _api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/currencies/\".concat(id));\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/master-data.service.ts\n"));

/***/ })

});