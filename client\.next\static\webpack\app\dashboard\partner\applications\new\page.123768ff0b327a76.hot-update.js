"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./data/mock-data.ts":
/*!***************************!*\
  !*** ./data/mock-data.ts ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   demoOrganization: () => (/* binding */ demoOrganization),\n/* harmony export */   documentTypes: () => (/* binding */ documentTypes),\n/* harmony export */   fileUploadConfig: () => (/* binding */ fileUploadConfig),\n/* harmony export */   mockBudgetTypes: () => (/* binding */ mockBudgetTypes),\n/* harmony export */   mockCentralLevels: () => (/* binding */ mockCentralLevels),\n/* harmony export */   mockCurrencies: () => (/* binding */ mockCurrencies),\n/* harmony export */   mockDomains: () => (/* binding */ mockDomains),\n/* harmony export */   mockFiscalYears: () => (/* binding */ mockFiscalYears),\n/* harmony export */   mockFundingSources: () => (/* binding */ mockFundingSources),\n/* harmony export */   mockFundingUnits: () => (/* binding */ mockFundingUnits),\n/* harmony export */   mockInputCategories: () => (/* binding */ mockInputCategories),\n/* harmony export */   mockProvinces: () => (/* binding */ mockProvinces)\n/* harmony export */ });\n// Mock data for dropdowns and selections\nconst mockFundingSources = [\n    {\n        id: \"1\",\n        name: \"Government\"\n    },\n    {\n        id: \"2\",\n        name: \"Private Sector\"\n    },\n    {\n        id: \"3\",\n        name: \"International Donors\"\n    },\n    {\n        id: \"4\",\n        name: \"NGOs\"\n    },\n    {\n        id: \"5\",\n        name: \"World Bank\"\n    },\n    {\n        id: \"6\",\n        name: \"African Development Bank\"\n    }\n];\nconst mockFundingUnits = [\n    {\n        id: \"1\",\n        name: \"Ministry of Health\"\n    },\n    {\n        id: \"2\",\n        name: \"Rwanda Biomedical Centre\"\n    },\n    {\n        id: \"3\",\n        name: \"University Teaching Hospital\"\n    },\n    {\n        id: \"4\",\n        name: \"District Hospitals\"\n    },\n    {\n        id: \"5\",\n        name: \"Health Centers\"\n    }\n];\nconst mockBudgetTypes = [\n    {\n        id: \"1\",\n        name: \"Operational\"\n    },\n    {\n        id: \"2\",\n        name: \"Capital\"\n    },\n    {\n        id: \"3\",\n        name: \"Project-based\"\n    },\n    {\n        id: \"4\",\n        name: \"Emergency\"\n    },\n    {\n        id: \"5\",\n        name: \"Development\"\n    }\n];\nconst mockCurrencies = [\n    {\n        id: \"USD\",\n        code: \"USD\",\n        name: \"US Dollar\",\n        symbol: \"$\"\n    },\n    {\n        id: \"EUR\",\n        code: \"EUR\",\n        name: \"Euro\",\n        symbol: \"€\"\n    },\n    {\n        id: \"RWF\",\n        code: \"RWF\",\n        name: \"Rwandan Franc\",\n        symbol: \"RWF\"\n    },\n    {\n        id: \"GBP\",\n        code: \"GBP\",\n        name: \"British Pound\",\n        symbol: \"£\"\n    }\n];\n// Generate fiscal years dynamically based on current year\nconst getCurrentFiscalYears = ()=>{\n    const currentYear = new Date().getFullYear();\n    const currentFiscalYear = \"\".concat(currentYear, \"-\").concat(currentYear + 1);\n    const nextFiscalYear = \"\".concat(currentYear + 1, \"-\").concat(currentYear + 2);\n    return [\n        {\n            id: currentFiscalYear,\n            year: currentFiscalYear,\n            name: currentFiscalYear\n        },\n        {\n            id: nextFiscalYear,\n            year: nextFiscalYear,\n            name: nextFiscalYear\n        }\n    ];\n};\nconst mockFiscalYears = getCurrentFiscalYears();\n// Domain Interventions with cascading structure\nconst mockDomains = [\n    {\n        id: \"1\",\n        name: \"Health Service Delivery\",\n        subDomains: [\n            {\n                id: \"1-1\",\n                name: \"Primary Healthcare\",\n                functions: [\n                    {\n                        id: \"1-1-1\",\n                        name: \"Preventive Care\",\n                        subFunctions: [\n                            {\n                                id: \"1-1-1-1\",\n                                name: \"Vaccination Programs\"\n                            },\n                            {\n                                id: \"1-1-1-2\",\n                                name: \"Health Screening\"\n                            },\n                            {\n                                id: \"1-1-1-3\",\n                                name: \"Health Education\"\n                            }\n                        ]\n                    },\n                    {\n                        id: \"1-1-2\",\n                        name: \"Curative Care\",\n                        subFunctions: [\n                            {\n                                id: \"1-1-2-1\",\n                                name: \"Outpatient Services\"\n                            },\n                            {\n                                id: \"1-1-2-2\",\n                                name: \"Emergency Care\"\n                            },\n                            {\n                                id: \"1-1-2-3\",\n                                name: \"Chronic Disease Management\"\n                            }\n                        ]\n                    }\n                ]\n            },\n            {\n                id: \"1-2\",\n                name: \"Secondary Healthcare\",\n                functions: [\n                    {\n                        id: \"1-2-1\",\n                        name: \"Specialized Services\",\n                        subFunctions: [\n                            {\n                                id: \"1-2-1-1\",\n                                name: \"Surgery\"\n                            },\n                            {\n                                id: \"1-2-1-2\",\n                                name: \"Diagnostics\"\n                            },\n                            {\n                                id: \"1-2-1-3\",\n                                name: \"Rehabilitation\"\n                            }\n                        ]\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"Health Workforce\",\n        subDomains: [\n            {\n                id: \"2-1\",\n                name: \"Training & Development\",\n                functions: [\n                    {\n                        id: \"2-1-1\",\n                        name: \"Professional Training\",\n                        subFunctions: [\n                            {\n                                id: \"2-1-1-1\",\n                                name: \"Medical Education\"\n                            },\n                            {\n                                id: \"2-1-1-2\",\n                                name: \"Nursing Education\"\n                            },\n                            {\n                                id: \"2-1-1-3\",\n                                name: \"Continuing Education\"\n                            }\n                        ]\n                    }\n                ]\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        name: \"Health Information Systems\",\n        subDomains: [\n            {\n                id: \"3-1\",\n                name: \"Data Management\",\n                functions: [\n                    {\n                        id: \"3-1-1\",\n                        name: \"Health Records\",\n                        subFunctions: [\n                            {\n                                id: \"3-1-1-1\",\n                                name: \"Electronic Health Records\"\n                            },\n                            {\n                                id: \"3-1-1-2\",\n                                name: \"Patient Registration\"\n                            },\n                            {\n                                id: \"3-1-1-3\",\n                                name: \"Health Analytics\"\n                            }\n                        ]\n                    }\n                ]\n            }\n        ]\n    }\n];\n// Input Categories with Activity Inputs\nconst mockInputCategories = [\n    {\n        id: \"1\",\n        name: \"Human Resources\",\n        activityInputs: [\n            {\n                id: \"1-1\",\n                name: \"Medical Staff\"\n            },\n            {\n                id: \"1-2\",\n                name: \"Nursing Staff\"\n            },\n            {\n                id: \"1-3\",\n                name: \"Administrative Staff\"\n            },\n            {\n                id: \"1-4\",\n                name: \"Technical Staff\"\n            },\n            {\n                id: \"1-5\",\n                name: \"Support Staff\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"Medical Equipment\",\n        activityInputs: [\n            {\n                id: \"2-1\",\n                name: \"Diagnostic Equipment\"\n            },\n            {\n                id: \"2-2\",\n                name: \"Treatment Equipment\"\n            },\n            {\n                id: \"2-3\",\n                name: \"Laboratory Equipment\"\n            },\n            {\n                id: \"2-4\",\n                name: \"Surgical Equipment\"\n            },\n            {\n                id: \"2-5\",\n                name: \"Emergency Equipment\"\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        name: \"Pharmaceuticals\",\n        activityInputs: [\n            {\n                id: \"3-1\",\n                name: \"Essential Medicines\"\n            },\n            {\n                id: \"3-2\",\n                name: \"Vaccines\"\n            },\n            {\n                id: \"3-3\",\n                name: \"Medical Supplies\"\n            },\n            {\n                id: \"3-4\",\n                name: \"Laboratory Reagents\"\n            },\n            {\n                id: \"3-5\",\n                name: \"Contraceptives\"\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        name: \"Infrastructure\",\n        activityInputs: [\n            {\n                id: \"4-1\",\n                name: \"Building Construction\"\n            },\n            {\n                id: \"4-2\",\n                name: \"Renovation\"\n            },\n            {\n                id: \"4-3\",\n                name: \"Utilities\"\n            },\n            {\n                id: \"4-4\",\n                name: \"Transportation\"\n            },\n            {\n                id: \"4-5\",\n                name: \"ICT Infrastructure\"\n            }\n        ]\n    },\n    {\n        id: \"5\",\n        name: \"Training & Capacity Building\",\n        activityInputs: [\n            {\n                id: \"5-1\",\n                name: \"Professional Development\"\n            },\n            {\n                id: \"5-2\",\n                name: \"Skills Training\"\n            },\n            {\n                id: \"5-3\",\n                name: \"Leadership Development\"\n            },\n            {\n                id: \"5-4\",\n                name: \"Technical Training\"\n            },\n            {\n                id: \"5-5\",\n                name: \"Community Health Training\"\n            }\n        ]\n    }\n];\n// Provinces with Districts\nconst mockProvinces = [\n    {\n        id: \"1\",\n        name: \"Kigali City\",\n        districts: [\n            {\n                id: \"1-1\",\n                name: \"Gasabo\"\n            },\n            {\n                id: \"1-2\",\n                name: \"Kicukiro\"\n            },\n            {\n                id: \"1-3\",\n                name: \"Nyarugenge\"\n            }\n        ]\n    },\n    {\n        id: \"2\",\n        name: \"Eastern Province\",\n        districts: [\n            {\n                id: \"2-1\",\n                name: \"Bugesera\"\n            },\n            {\n                id: \"2-2\",\n                name: \"Gatsibo\"\n            },\n            {\n                id: \"2-3\",\n                name: \"Kayonza\"\n            },\n            {\n                id: \"2-4\",\n                name: \"Kirehe\"\n            },\n            {\n                id: \"2-5\",\n                name: \"Ngoma\"\n            },\n            {\n                id: \"2-6\",\n                name: \"Nyagatare\"\n            },\n            {\n                id: \"2-7\",\n                name: \"Rwamagana\"\n            }\n        ]\n    },\n    {\n        id: \"3\",\n        name: \"Northern Province\",\n        districts: [\n            {\n                id: \"3-1\",\n                name: \"Burera\"\n            },\n            {\n                id: \"3-2\",\n                name: \"Gakenke\"\n            },\n            {\n                id: \"3-3\",\n                name: \"Gicumbi\"\n            },\n            {\n                id: \"3-4\",\n                name: \"Musanze\"\n            },\n            {\n                id: \"3-5\",\n                name: \"Rulindo\"\n            }\n        ]\n    },\n    {\n        id: \"4\",\n        name: \"Southern Province\",\n        districts: [\n            {\n                id: \"4-1\",\n                name: \"Gisagara\"\n            },\n            {\n                id: \"4-2\",\n                name: \"Huye\"\n            },\n            {\n                id: \"4-3\",\n                name: \"Kamonyi\"\n            },\n            {\n                id: \"4-4\",\n                name: \"Muhanga\"\n            },\n            {\n                id: \"4-5\",\n                name: \"Nyamagabe\"\n            },\n            {\n                id: \"4-6\",\n                name: \"Nyanza\"\n            },\n            {\n                id: \"4-7\",\n                name: \"Nyaruguru\"\n            },\n            {\n                id: \"4-8\",\n                name: \"Ruhango\"\n            }\n        ]\n    },\n    {\n        id: \"5\",\n        name: \"Western Province\",\n        districts: [\n            {\n                id: \"5-1\",\n                name: \"Karongi\"\n            },\n            {\n                id: \"5-2\",\n                name: \"Ngororero\"\n            },\n            {\n                id: \"5-3\",\n                name: \"Nyabihu\"\n            },\n            {\n                id: \"5-4\",\n                name: \"Nyamasheke\"\n            },\n            {\n                id: \"5-5\",\n                name: \"Rubavu\"\n            },\n            {\n                id: \"5-6\",\n                name: \"Rusizi\"\n            },\n            {\n                id: \"5-7\",\n                name: \"Rutsiro\"\n            }\n        ]\n    }\n];\n// Central Levels\nconst mockCentralLevels = [\n    {\n        id: \"1\",\n        name: \"Ministry of Health\"\n    },\n    {\n        id: \"2\",\n        name: \"Rwanda Biomedical Centre\"\n    },\n    {\n        id: \"3\",\n        name: \"University Teaching Hospital of Butare\"\n    },\n    {\n        id: \"4\",\n        name: \"University Teaching Hospital of Kigali\"\n    },\n    {\n        id: \"5\",\n        name: \"Rwanda Military Hospital\"\n    },\n    {\n        id: \"6\",\n        name: \"King Faisal Hospital\"\n    },\n    {\n        id: \"7\",\n        name: \"National Reference Laboratories\"\n    },\n    {\n        id: \"8\",\n        name: \"Rwanda Food and Drug Authority\"\n    },\n    {\n        id: \"9\",\n        name: \"Community Health Cooperatives\"\n    },\n    {\n        id: \"10\",\n        name: \"Health Development Initiative\"\n    }\n];\n// Document Types for Upload\nconst documentTypes = [\n    {\n        id: \"MEMO_OBJECTIVE\",\n        name: \"Memo Describing The Long Term Objective\",\n        description: \"Document outlining the long-term objectives of the MoU\",\n        required: true,\n        order: 1\n    },\n    {\n        id: \"STRATEGIC_PLAN\",\n        name: \"Strategic Plan\",\n        description: \"Strategic plan document for the partnership\",\n        required: true,\n        order: 2\n    },\n    {\n        id: \"CAPACITY_BUILDING\",\n        name: \"Capacity Building Transfer Plan\",\n        description: \"Plan for capacity building and knowledge transfer\",\n        required: true,\n        order: 3\n    },\n    {\n        id: \"MEMO_FUNDS\",\n        name: \"Memo Describing the Source of Funds\",\n        description: \"Document describing funding sources and financial arrangements\",\n        required: true,\n        order: 4\n    }\n];\n// Accepted file types and size limits\nconst fileUploadConfig = {\n    maxSize: 10 * 1024 * 1024,\n    acceptedTypes: [\n        'application/pdf',\n        'application/msword',\n        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n        'application/vnd.ms-excel',\n        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n        'image/jpeg',\n        'image/png',\n        'image/gif'\n    ],\n    acceptedExtensions: [\n        '.pdf',\n        '.doc',\n        '.docx',\n        '.xls',\n        '.xlsx',\n        '.jpg',\n        '.jpeg',\n        '.png',\n        '.gif'\n    ]\n};\n// Demo organization data (for pre-filled fields)\nconst demoOrganization = {\n    id: 1,\n    name: \"Rwanda Health Partners Initiative\",\n    type: \"International NGO\",\n    registrationNumber: \"RHP-2024-001\",\n    contactPerson: \"Dr. Sarah Johnson\",\n    email: \"<EMAIL>\",\n    phone: \"+250 788 123 456\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./data/mock-data.ts\n"));

/***/ })

});