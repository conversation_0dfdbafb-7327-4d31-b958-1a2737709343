"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/new-activities-step.tsx":
/*!*********************************************************!*\
  !*** ./components/wizard-steps/new-activities-step.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewActivitiesStep: () => (/* binding */ NewActivitiesStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/.pnpm/@hookform+resolvers@3.3.4_r_ffdcfe076cdc90c8a3b00ad082065b78/node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-auto-save */ \"(app-pages-browser)/./hooks/use-auto-save.ts\");\n/* harmony import */ var _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/mou-application-store */ \"(app-pages-browser)/./store/mou-application-store.ts\");\n/* harmony import */ var _lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/validations/mou-application */ \"(app-pages-browser)/./lib/validations/mou-application.ts\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Info,MapPin,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ NewActivitiesStep auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewActivitiesStep() {\n    _s();\n    const { onBlur } = (0,_hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave)();\n    const { data, addActivity, removeActivity, updateActivity } = (0,_store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore)();\n    const [dropdownData, setDropdownData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        domains: mockDomains,\n        inputCategories: mockInputCategories,\n        provinces: mockProvinces,\n        centralLevels: mockCentralLevels,\n        fiscalYears: mockFiscalYears.filter({\n            \"NewActivitiesStep.useState\": (fy)=>fy.year === '2024-2025' || fy.year === '2025-2026'\n        }[\"NewActivitiesStep.useState\"]) // Only current and next fiscal year\n    });\n    const { control, register, handleSubmit, watch, setValue, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__.activitiesSchema),\n        defaultValues: {\n            activities: data.activities.length > 0 ? data.activities : []\n        }\n    });\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useFieldArray)({\n        control,\n        name: \"activities\"\n    });\n    const activities = watch('activities');\n    // Get available projects for activity assignment\n    const availableProjects = data.projects.map((project)=>({\n            id: project.id,\n            name: project.name\n        }));\n    // Add new activity\n    const handleAddActivity = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[handleAddActivity]\": ()=>{\n            const newActivity = {\n                projectId: '',\n                name: '',\n                implementor: '',\n                implementingUnit: '',\n                fiscalYear: '',\n                startDate: '',\n                endDate: '',\n                domain: '',\n                subDomain: '',\n                subDomainFunction: '',\n                subFunction: '',\n                inputCategory: '',\n                activityInput: '',\n                geographicLevel: 'Provinces',\n                budgetAllocations: []\n            };\n            append(newActivity);\n            addActivity(newActivity);\n        }\n    }[\"NewActivitiesStep.useCallback[handleAddActivity]\"], [\n        append,\n        addActivity\n    ]);\n    // Remove activity\n    const handleRemoveActivity = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[handleRemoveActivity]\": (index)=>{\n            var _data_activities_index;\n            const activityId = (_data_activities_index = data.activities[index]) === null || _data_activities_index === void 0 ? void 0 : _data_activities_index.id;\n            if (activityId) {\n                removeActivity(activityId);\n            }\n            remove(index);\n        }\n    }[\"NewActivitiesStep.useCallback[handleRemoveActivity]\"], [\n        remove,\n        removeActivity,\n        data.activities\n    ]);\n    // Update activity in store when form changes\n    const handleActivityUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[handleActivityUpdate]\": (index, field, value)=>{\n            var _data_activities_index;\n            const activityId = (_data_activities_index = data.activities[index]) === null || _data_activities_index === void 0 ? void 0 : _data_activities_index.id;\n            if (activityId) {\n                updateActivity(activityId, {\n                    [field]: value\n                });\n            }\n        }\n    }[\"NewActivitiesStep.useCallback[handleActivityUpdate]\"], [\n        updateActivity,\n        data.activities\n    ]);\n    // Get cascading dropdown options\n    const getSubDomains = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getSubDomains]\": (domainId)=>{\n            const domain = dropdownData.domains.find({\n                \"NewActivitiesStep.useCallback[getSubDomains].domain\": (d)=>d.id === domainId\n            }[\"NewActivitiesStep.useCallback[getSubDomains].domain\"]);\n            return (domain === null || domain === void 0 ? void 0 : domain.subDomains) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getSubDomains]\"], [\n        dropdownData.domains\n    ]);\n    const getDomainFunctions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getDomainFunctions]\": (domainId, subDomainId)=>{\n            const domain = dropdownData.domains.find({\n                \"NewActivitiesStep.useCallback[getDomainFunctions].domain\": (d)=>d.id === domainId\n            }[\"NewActivitiesStep.useCallback[getDomainFunctions].domain\"]);\n            const subDomain = domain === null || domain === void 0 ? void 0 : domain.subDomains.find({\n                \"NewActivitiesStep.useCallback[getDomainFunctions]\": (sd)=>sd.id === subDomainId\n            }[\"NewActivitiesStep.useCallback[getDomainFunctions]\"]);\n            return (subDomain === null || subDomain === void 0 ? void 0 : subDomain.functions) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getDomainFunctions]\"], [\n        dropdownData.domains\n    ]);\n    const getSubFunctions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getSubFunctions]\": (domainId, subDomainId, functionId)=>{\n            const domain = dropdownData.domains.find({\n                \"NewActivitiesStep.useCallback[getSubFunctions].domain\": (d)=>d.id === domainId\n            }[\"NewActivitiesStep.useCallback[getSubFunctions].domain\"]);\n            const subDomain = domain === null || domain === void 0 ? void 0 : domain.subDomains.find({\n                \"NewActivitiesStep.useCallback[getSubFunctions]\": (sd)=>sd.id === subDomainId\n            }[\"NewActivitiesStep.useCallback[getSubFunctions]\"]);\n            const domainFunction = subDomain === null || subDomain === void 0 ? void 0 : subDomain.functions.find({\n                \"NewActivitiesStep.useCallback[getSubFunctions]\": (f)=>f.id === functionId\n            }[\"NewActivitiesStep.useCallback[getSubFunctions]\"]);\n            return (domainFunction === null || domainFunction === void 0 ? void 0 : domainFunction.subFunctions) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getSubFunctions]\"], [\n        dropdownData.domains\n    ]);\n    const getActivityInputs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getActivityInputs]\": (categoryId)=>{\n            const category = dropdownData.inputCategories.find({\n                \"NewActivitiesStep.useCallback[getActivityInputs].category\": (c)=>c.id === categoryId\n            }[\"NewActivitiesStep.useCallback[getActivityInputs].category\"]);\n            return (category === null || category === void 0 ? void 0 : category.inputs) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getActivityInputs]\"], [\n        dropdownData.inputCategories\n    ]);\n    const getDistricts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewActivitiesStep.useCallback[getDistricts]\": (provinceId)=>{\n            const province = dropdownData.provinces.find({\n                \"NewActivitiesStep.useCallback[getDistricts].province\": (p)=>p.id === provinceId\n            }[\"NewActivitiesStep.useCallback[getDistricts].province\"]);\n            return (province === null || province === void 0 ? void 0 : province.districts) || [];\n        }\n    }[\"NewActivitiesStep.useCallback[getDistricts]\"], [\n        dropdownData.provinces\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this),\n                                \"Activity Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            children: \"Create activities for each project with detailed implementation information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            availableProjects.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: \"No projects available. Please add at least one project in the previous step before creating activities.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, this),\n            availableProjects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: fields.map((field, index)=>{\n                    var _errors_activities_index, _errors_activities, _errors_activities_index_projectId, _errors_activities_index1, _errors_activities_index2, _errors_activities1, _errors_activities_index_name, _errors_activities_index3, _errors_activities_index4, _errors_activities2, _errors_activities_index_implementor, _errors_activities_index5, _errors_activities_index6, _errors_activities3, _errors_activities_index_implementingUnit, _errors_activities_index7, _errors_activities_index8, _errors_activities4, _errors_activities_index_fiscalYear, _errors_activities_index9, _errors_activities_index10, _errors_activities5, _errors_activities_index_startDate, _errors_activities_index11, _errors_activities_index12, _errors_activities6, _errors_activities_index_endDate, _errors_activities_index13, _activities_index, _activities_index1, _activities_index2, _activities_index3, _activities_index4, _activities_index5, _activities_index6, _activities_index7, _activities_index8, _activities_index9, _activities_index10, _activities_index11, _activities_index12, _activities_index_budgetAllocations, _activities_index13;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Activity \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleRemoveActivity(index),\n                                            className: \"text-destructive hover:text-destructive\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Remove\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".projectId\"),\n                                                        children: [\n                                                            \"Assign to Project \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"activities.\".concat(index, \".projectId\"), value);\n                                                            handleActivityUpdate(index, 'projectId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select project\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: availableProjects.map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: project.id,\n                                                                        children: project.name\n                                                                    }, project.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 196,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities = errors.activities) === null || _errors_activities === void 0 ? void 0 : (_errors_activities_index = _errors_activities[index]) === null || _errors_activities_index === void 0 ? void 0 : _errors_activities_index.projectId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index1 = errors.activities[index]) === null || _errors_activities_index1 === void 0 ? void 0 : (_errors_activities_index_projectId = _errors_activities_index1.projectId) === null || _errors_activities_index_projectId === void 0 ? void 0 : _errors_activities_index_projectId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".name\"),\n                                                        children: [\n                                                            \"Activity Name \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 37\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".name\"),\n                                                        ...register(\"activities.\".concat(index, \".name\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'name', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter activity name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities1 = errors.activities) === null || _errors_activities1 === void 0 ? void 0 : (_errors_activities_index2 = _errors_activities1[index]) === null || _errors_activities_index2 === void 0 ? void 0 : _errors_activities_index2.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index3 = errors.activities[index]) === null || _errors_activities_index3 === void 0 ? void 0 : (_errors_activities_index_name = _errors_activities_index3.name) === null || _errors_activities_index_name === void 0 ? void 0 : _errors_activities_index_name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".implementor\"),\n                                                        children: [\n                                                            \"Implementor \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".implementor\"),\n                                                        ...register(\"activities.\".concat(index, \".implementor\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'implementor', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter implementor name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities2 = errors.activities) === null || _errors_activities2 === void 0 ? void 0 : (_errors_activities_index4 = _errors_activities2[index]) === null || _errors_activities_index4 === void 0 ? void 0 : _errors_activities_index4.implementor) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index5 = errors.activities[index]) === null || _errors_activities_index5 === void 0 ? void 0 : (_errors_activities_index_implementor = _errors_activities_index5.implementor) === null || _errors_activities_index_implementor === void 0 ? void 0 : _errors_activities_index_implementor.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".implementingUnit\"),\n                                                        children: [\n                                                            \"Implementing Unit \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".implementingUnit\"),\n                                                        ...register(\"activities.\".concat(index, \".implementingUnit\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'implementingUnit', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter implementing unit\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities3 = errors.activities) === null || _errors_activities3 === void 0 ? void 0 : (_errors_activities_index6 = _errors_activities3[index]) === null || _errors_activities_index6 === void 0 ? void 0 : _errors_activities_index6.implementingUnit) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index7 = errors.activities[index]) === null || _errors_activities_index7 === void 0 ? void 0 : (_errors_activities_index_implementingUnit = _errors_activities_index7.implementingUnit) === null || _errors_activities_index_implementingUnit === void 0 ? void 0 : _errors_activities_index_implementingUnit.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".fiscalYear\"),\n                                                        children: [\n                                                            \"Fiscal Year \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"activities.\".concat(index, \".fiscalYear\"), value);\n                                                            handleActivityUpdate(index, 'fiscalYear', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select fiscal year\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.fiscalYears.map((fy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: fy.year,\n                                                                        children: fy.year\n                                                                    }, fy.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 294,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities4 = errors.activities) === null || _errors_activities4 === void 0 ? void 0 : (_errors_activities_index8 = _errors_activities4[index]) === null || _errors_activities_index8 === void 0 ? void 0 : _errors_activities_index8.fiscalYear) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index9 = errors.activities[index]) === null || _errors_activities_index9 === void 0 ? void 0 : (_errors_activities_index_fiscalYear = _errors_activities_index9.fiscalYear) === null || _errors_activities_index_fiscalYear === void 0 ? void 0 : _errors_activities_index_fiscalYear.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".startDate\"),\n                                                        children: [\n                                                            \"Start Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".startDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"activities.\".concat(index, \".startDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'startDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities5 = errors.activities) === null || _errors_activities5 === void 0 ? void 0 : (_errors_activities_index10 = _errors_activities5[index]) === null || _errors_activities_index10 === void 0 ? void 0 : _errors_activities_index10.startDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index11 = errors.activities[index]) === null || _errors_activities_index11 === void 0 ? void 0 : (_errors_activities_index_startDate = _errors_activities_index11.startDate) === null || _errors_activities_index_startDate === void 0 ? void 0 : _errors_activities_index_startDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".endDate\"),\n                                                        children: [\n                                                            \"End Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"activities.\".concat(index, \".endDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"activities.\".concat(index, \".endDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleActivityUpdate(index, 'endDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    ((_errors_activities6 = errors.activities) === null || _errors_activities6 === void 0 ? void 0 : (_errors_activities_index12 = _errors_activities6[index]) === null || _errors_activities_index12 === void 0 ? void 0 : _errors_activities_index12.endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_activities_index13 = errors.activities[index]) === null || _errors_activities_index13 === void 0 ? void 0 : (_errors_activities_index_endDate = _errors_activities_index13.endDate) === null || _errors_activities_index_endDate === void 0 ? void 0 : _errors_activities_index_endDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-base font-medium\",\n                                                children: [\n                                                    \"Domain of Intervention \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 44\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".domain\"),\n                                                                children: \"Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".domain\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".subDomain\"), '');\n                                                                    setValue(\"activities.\".concat(index, \".subDomainFunction\"), '');\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), '');\n                                                                    handleActivityUpdate(index, 'domain', value);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select domain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 372,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 371,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: dropdownData.domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: domain.id,\n                                                                                children: domain.name\n                                                                            }, domain.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 376,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".subDomain\"),\n                                                                children: \"Sub Domain\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".subDomain\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".subDomainFunction\"), '');\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), '');\n                                                                    handleActivityUpdate(index, 'subDomain', value);\n                                                                },\n                                                                disabled: !((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.domain),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select sub domain\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 397,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 396,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getSubDomains(((_activities_index1 = activities[index]) === null || _activities_index1 === void 0 ? void 0 : _activities_index1.domain) || '').map((subDomain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: subDomain.id,\n                                                                                children: subDomain.name\n                                                                            }, subDomain.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".subDomainFunction\"),\n                                                                children: \"Sub Domain Function\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".subDomainFunction\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), '');\n                                                                    handleActivityUpdate(index, 'subDomainFunction', value);\n                                                                },\n                                                                disabled: !((_activities_index2 = activities[index]) === null || _activities_index2 === void 0 ? void 0 : _activities_index2.subDomain),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select function\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 421,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getDomainFunctions(((_activities_index3 = activities[index]) === null || _activities_index3 === void 0 ? void 0 : _activities_index3.domain) || '', ((_activities_index4 = activities[index]) === null || _activities_index4 === void 0 ? void 0 : _activities_index4.subDomain) || '').map((func)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: func.id,\n                                                                                children: func.name\n                                                                            }, func.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".subFunction\"),\n                                                                children: \"Sub Function\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".subFunction\"), value);\n                                                                    handleActivityUpdate(index, 'subFunction', value);\n                                                                },\n                                                                disabled: !((_activities_index5 = activities[index]) === null || _activities_index5 === void 0 ? void 0 : _activities_index5.subDomainFunction),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select sub function\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 447,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getSubFunctions(((_activities_index6 = activities[index]) === null || _activities_index6 === void 0 ? void 0 : _activities_index6.domain) || '', ((_activities_index7 = activities[index]) === null || _activities_index7 === void 0 ? void 0 : _activities_index7.subDomain) || '', ((_activities_index8 = activities[index]) === null || _activities_index8 === void 0 ? void 0 : _activities_index8.subDomainFunction) || '').map((subFunc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: subFunc.id,\n                                                                                children: subFunc.name\n                                                                            }, subFunc.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 455,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 449,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-base font-medium\",\n                                                children: [\n                                                    \"Budget Allocation \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-destructive\",\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 39\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".inputCategory\"),\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".inputCategory\"), value);\n                                                                    setValue(\"activities.\".concat(index, \".activityInput\"), '');\n                                                                    handleActivityUpdate(index, 'inputCategory', value);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select category\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 482,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: dropdownData.inputCategories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: category.id,\n                                                                                children: category.name\n                                                                            }, category.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 487,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                htmlFor: \"activities.\".concat(index, \".activityInput\"),\n                                                                children: \"Activity Input\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                onValueChange: (value)=>{\n                                                                    setValue(\"activities.\".concat(index, \".activityInput\"), value);\n                                                                    handleActivityUpdate(index, 'activityInput', value);\n                                                                },\n                                                                disabled: !((_activities_index9 = activities[index]) === null || _activities_index9 === void 0 ? void 0 : _activities_index9.inputCategory),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                            placeholder: \"Select activity input\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                        children: getActivityInputs(((_activities_index10 = activities[index]) === null || _activities_index10 === void 0 ? void 0 : _activities_index10.inputCategory) || '').map((input)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                value: input.id,\n                                                                                children: input.name\n                                                                            }, input.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                lineNumber: 510,\n                                                                                columnNumber: 29\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: [\n                                                            \"Location Selection \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"activities.\".concat(index, \".geographicLevel\"),\n                                                        children: \"Geographic Level\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"activities.\".concat(index, \".geographicLevel\"), value);\n                                                            setValue(\"activities.\".concat(index, \".budgetAllocations\"), []);\n                                                            handleActivityUpdate(index, 'geographicLevel', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select geographic level\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"Provinces\",\n                                                                        children: \"Provinces\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 543,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: \"Central\",\n                                                                        children: \"Central Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 544,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 19\n                                            }, this),\n                                            ((_activities_index11 = activities[index]) === null || _activities_index11 === void 0 ? void 0 : _activities_index11.geographicLevel) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Budget Allocations\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _activities_index;\n                                                                    const currentAllocations = ((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.budgetAllocations) || [];\n                                                                    const newAllocation = {\n                                                                        location: '',\n                                                                        budget: 0\n                                                                    };\n                                                                    setValue(\"activities.\".concat(index, \".budgetAllocations\"), [\n                                                                        ...currentAllocations,\n                                                                        newAllocation\n                                                                    ]);\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Add \",\n                                                                    ((_activities_index12 = activities[index]) === null || _activities_index12 === void 0 ? void 0 : _activities_index12.geographicLevel) === 'Provinces' ? 'Province' : 'Central Level'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    (_activities_index13 = activities[index]) === null || _activities_index13 === void 0 ? void 0 : (_activities_index_budgetAllocations = _activities_index13.budgetAllocations) === null || _activities_index_budgetAllocations === void 0 ? void 0 : _activities_index_budgetAllocations.map((allocation, allocIndex)=>{\n                                                        var _activities_index, _activities_index1, _activities_index2;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 p-3 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: ((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.geographicLevel) === 'Provinces' ? 'Province' : 'Central Level'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 575,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                            onValueChange: (value)=>{\n                                                                                var _activities_index;\n                                                                                const currentAllocations = [\n                                                                                    ...((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.budgetAllocations) || []\n                                                                                ];\n                                                                                currentAllocations[allocIndex] = {\n                                                                                    ...currentAllocations[allocIndex],\n                                                                                    location: value\n                                                                                };\n                                                                                setValue(\"activities.\".concat(index, \".budgetAllocations\"), currentAllocations);\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                        placeholder: \"Select \".concat(((_activities_index1 = activities[index]) === null || _activities_index1 === void 0 ? void 0 : _activities_index1.geographicLevel) === 'Provinces' ? 'province' : 'central level')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                        lineNumber: 586,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                    lineNumber: 585,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                                    children: ((_activities_index2 = activities[index]) === null || _activities_index2 === void 0 ? void 0 : _activities_index2.geographicLevel) === 'Provinces' ? dropdownData.provinces.map((province)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: province.id,\n                                                                                            children: province.name\n                                                                                        }, province.id, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                            lineNumber: 591,\n                                                                                            columnNumber: 39\n                                                                                        }, this)) : dropdownData.centralLevels.map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                            value: level.id,\n                                                                                            children: level.name\n                                                                                        }, level.id, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                            lineNumber: 596,\n                                                                                            columnNumber: 39\n                                                                                        }, this))\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                                    lineNumber: 588,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                            children: \"Budget Amount\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                            type: \"number\",\n                                                                            min: \"0\",\n                                                                            step: \"0.01\",\n                                                                            ...register(\"activities.\".concat(index, \".budgetAllocations.\").concat(allocIndex, \".budget\"), {\n                                                                                valueAsNumber: true,\n                                                                                onBlur: onBlur\n                                                                            }),\n                                                                            placeholder: \"Enter budget\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 605,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-end\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>{\n                                                                            var _activities_index;\n                                                                            const currentAllocations = [\n                                                                                ...((_activities_index = activities[index]) === null || _activities_index === void 0 ? void 0 : _activities_index.budgetAllocations) || []\n                                                                            ];\n                                                                            currentAllocations.splice(allocIndex, 1);\n                                                                            setValue(\"activities.\".concat(index, \".budgetAllocations\"), currentAllocations);\n                                                                        },\n                                                                        className: \"text-destructive hover:text-destructive\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                            lineNumber: 631,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                                    lineNumber: 619,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, allocIndex, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, field.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            availableProjects.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-dashed\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: handleAddActivity,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                    lineNumber: 655,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Activity\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 649,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground mt-2\",\n                            children: \"Create activities for your projects\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                    lineNumber: 648,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 647,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_Info_MapPin_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 667,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Note:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            \" Activities represent specific tasks or initiatives within your projects. Ensure budget allocations don't exceed the total project budget.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-activities-step.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s(NewActivitiesStep, \"QyVUAAzIsYPdJbdIK9YpcIDm7m4=\", false, function() {\n    return [\n        _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave,\n        _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_12__.useFieldArray\n    ];\n});\n_c = NewActivitiesStep;\nvar _c;\n$RefreshReg$(_c, \"NewActivitiesStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/new-activities-step.tsx\n"));

/***/ })

});