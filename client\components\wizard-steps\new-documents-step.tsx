"use client"

import { useCallback, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FileUpload } from '@/components/ui/file-upload'
import { useFieldBlurAutoSave } from '@/hooks/use-auto-save'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { documentsSchema } from '@/lib/validations/mou-application'
import { documentTypes } from '@/data/mock-data'
import { FileText, AlertTriangle, Info, CheckCircle, Upload } from 'lucide-react'
import { DocumentType } from '@/types/mou-application'

type FormData = z.infer<typeof documentsSchema>

export function NewDocumentsStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const { data, uploadDocument, removeDocument, updateDocumentProgress } = useMouApplicationStore()
  const [uploadingStates, setUploadingStates] = useState<Record<DocumentType, boolean>>({
    MEMO_OBJECTIVE: false,
    STRATEGIC_PLAN: false,
    CAPACITY_BUILDING: false,
    MEMO_FUNDS: false
  })

  const {
    formState: { errors },
    trigger
  } = useForm<FormData>({
    resolver: zodResolver(documentsSchema),
    defaultValues: {
      documents: {
        MEMO_OBJECTIVE: data.documents.find(d => d.type === 'MEMO_OBJECTIVE')?.file || null,
        STRATEGIC_PLAN: data.documents.find(d => d.type === 'STRATEGIC_PLAN')?.file || null,
        CAPACITY_BUILDING: data.documents.find(d => d.type === 'CAPACITY_BUILDING')?.file || null,
        MEMO_FUNDS: data.documents.find(d => d.type === 'MEMO_FUNDS')?.file || null
      }
    }
  })

  // Handle file upload with progress simulation
  const handleFileUpload = useCallback(async (documentType: DocumentType, file: File | null) => {
    if (!file) {
      removeDocument(documentType)
      return
    }

    try {
      setUploadingStates(prev => ({ ...prev, [documentType]: true }))
      
      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        updateDocumentProgress(documentType, progress)
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // Upload the document
      uploadDocument(documentType, file)
      
      // Trigger validation for this field
      await trigger(`documents.${documentType}`)
      
    } catch (error) {
      console.error('Upload failed:', error)
      removeDocument(documentType)
    } finally {
      setUploadingStates(prev => ({ ...prev, [documentType]: false }))
    }
  }, [uploadDocument, removeDocument, updateDocumentProgress, trigger])

  // Get document by type
  const getDocument = useCallback((type: DocumentType) => {
    return data.documents.find(d => d.type === type)
  }, [data.documents])

  // Check if all documents are uploaded
  const allDocumentsUploaded = documentTypes.every(docType => {
    const doc = getDocument(docType.id as DocumentType)
    return doc?.uploaded && doc?.file
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Required Documents
          </CardTitle>
          <CardDescription>
            Upload the following 4 required documents for your MoU application
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Progress Overview */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              <span className="font-medium">Upload Progress</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                {data.documents.filter(d => d.uploaded).length} of {documentTypes.length} uploaded
              </span>
              {allDocumentsUploaded && (
                <CheckCircle className="h-4 w-4 text-green-600" />
              )}
            </div>
          </div>
          <div className="w-full bg-muted rounded-full h-2 mt-3">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${(data.documents.filter(d => d.uploaded).length / documentTypes.length) * 100}%` 
              }}
            />
          </div>
        </CardContent>
      </Card>

      {/* Document Upload Cards */}
      <div className="space-y-4">
        {documentTypes.map((docType, index) => {
          const document = getDocument(docType.id as DocumentType)
          const isUploading = uploadingStates[docType.id as DocumentType]
          const hasError = errors.documents?.[docType.id as keyof typeof errors.documents]

          return (
            <Card key={docType.id} className={`relative ${hasError ? 'border-destructive' : ''}`}>
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <span className="flex h-6 w-6 items-center justify-center rounded-full bg-primary text-primary-foreground text-xs font-medium">
                        {index + 1}
                      </span>
                      {docType.name}
                      <span className="text-destructive">*</span>
                    </CardTitle>
                    <CardDescription>{docType.description}</CardDescription>
                  </div>
                  {document?.uploaded && (
                    <CheckCircle className="h-5 w-5 text-green-600 flex-shrink-0" />
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <FileUpload
                  onFileSelect={(file) => handleFileUpload(docType.id as DocumentType, file)}
                  currentFile={document?.file || null}
                  required={docType.required}
                  disabled={isUploading}
                  uploadProgress={isUploading ? document?.uploadProgress : undefined}
                  error={hasError ? String(hasError) : undefined}
                  label={`Upload ${docType.name}`}
                  description="Accepted formats: PDF, DOC, DOCX. Maximum size: 10MB"
                />
                
                {/* File Info */}
                {document?.file && (
                  <div className="mt-3 p-3 bg-muted rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm font-medium">{document.file.name}</span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {(document.file.size / (1024 * 1024)).toFixed(2)} MB
                      </span>
                    </div>
                    {document.uploaded && (
                      <div className="flex items-center gap-1 mt-2">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        <span className="text-xs text-green-600">Successfully uploaded</span>
                      </div>
                    )}
                  </div>
                )}

                {/* Error Display */}
                {hasError && (
                  <Alert variant="destructive" className="mt-3">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {String(hasError)}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Validation Alert */}
      {!allDocumentsUploaded && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            All 4 documents must be uploaded to proceed to the next step.
          </AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {allDocumentsUploaded && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            <strong>Great!</strong> All required documents have been uploaded successfully. 
            You can now proceed to review and submit your application.
          </AlertDescription>
        </Alert>
      )}

      {/* Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Document Requirements:</strong>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>All documents must be in PDF, DOC, or DOCX format</li>
            <li>Maximum file size is 10MB per document</li>
            <li>Documents should be clearly written and professionally formatted</li>
            <li>Ensure all information is accurate and up-to-date</li>
          </ul>
        </AlertDescription>
      </Alert>
    </div>
  )
}
