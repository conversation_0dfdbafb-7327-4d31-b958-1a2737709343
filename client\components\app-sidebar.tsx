"use client"

import * as React from "react"
import { useAuth } from "@/contexts/auth-context"
import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { PartnerNav } from "./partner-nav"
import { AdminNav } from "./admin-nav"
import {
  Menu,
  Shield,
  Building2,
  LogOut,
  FileText,
  CheckCircle,
  Clock,
  ChevronRight,
  Settings,
  DollarSign,
  Users,
  UserCircle,
  BarChart3,
} from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

export function AppSidebar() {
  const { user, logout } = useAuth()
  const pathname = usePathname()
  const [openSections, setOpenSections] = React.useState<string[]>(['mou-list', 'applications'])

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }

  const toggleSection = (section: string) => {
    setOpenSections(prev =>
      prev.includes(section)
        ? prev.filter(s => s !== section)
        : [...prev, section]
    )
  }

  if (user?.role === 'PARTNER') {
    return (
      <Sidebar className="border-r border-gray-200 bg-white shadow-lg">
        <SidebarHeader className="border-b border-gray-100 bg-gradient-to-r from-blue-600 to-indigo-600">
          <div className="flex items-center gap-4 px-6 py-5">
            <SidebarTrigger className="md:hidden">
              <Menu className="h-6 w-6 text-white" />
            </SidebarTrigger>
            <div className="flex items-center gap-4">
              <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-white/20 backdrop-blur-sm shadow-lg">
                <Building2 className="h-7 w-7 text-white" />
              </div>
              <div>
                <span className="text-xl font-bold text-white">MoH MoU System</span>
                <p className="text-sm text-blue-100 font-medium">Partner Portal</p>
              </div>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent className="bg-gray-50 px-4 py-6">
          <PartnerNav />
        </SidebarContent>

        <SidebarFooter className="border-t border-gray-200 bg-white">
          <div className="p-5">
            <div className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-100 shadow-sm">
              <Avatar className="h-12 w-12 ring-2 ring-blue-100">
                <AvatarImage src="/placeholder.svg" alt={user?.firstName} />
                <AvatarFallback className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold text-lg">
                  {user?.firstName?.[0]}
                  {user?.lastName?.[0]}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-base font-bold text-gray-900 truncate">
                    {user?.firstName} {user?.lastName}
                  </span>
                  <Badge
                    variant="secondary"
                    className={cn(
                      "text-xs px-2 py-1 font-semibold",
                      user?.role === 'ADMIN'
                        ? "bg-red-100 text-red-700"
                        : "bg-blue-100 text-blue-700"
                    )}
                  >
                    {user?.role}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 truncate font-medium">
                  {user?.email}
                </p>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => logout()}
                className="rounded-xl p-2 hover:bg-red-50 hover:text-red-600 transition-colors h-10 w-10"
                title="Logout"
              >
                <LogOut className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </SidebarFooter>
      </Sidebar>
    )
  }

  return (
    <Sidebar className="border-r border-gray-200 bg-white shadow-lg">
      <SidebarHeader className="border-b border-gray-100 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="flex items-center gap-4 px-6 py-5">
          <SidebarTrigger className="md:hidden">
            <Menu className="h-6 w-6 text-white" />
          </SidebarTrigger>
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-white/20 backdrop-blur-sm shadow-lg">
              <Shield className="h-7 w-7 text-white" />
            </div>
            <div>
              <span className="text-xl font-bold text-white">MoH MoU System</span>
              <p className="text-sm text-blue-100 font-medium">Admin Portal</p>
            </div>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent className="bg-gray-50 px-4 py-6">
        {/* Main Dashboard */}
        <div className="mb-6">
          <Link
            href="/dashboard/admin"
            className={cn(
              "group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg",
              isActive("/dashboard/admin")
                ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg"
                : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200"
            )}
          >
            <div className={cn(
              "flex h-12 w-12 items-center justify-center rounded-xl transition-colors",
              isActive("/dashboard/admin") ? "bg-white/20" : "bg-blue-100"
            )}>
              <BarChart3 className={cn(
                "h-6 w-6 transition-colors",
                isActive("/dashboard/admin") ? "text-white" : "text-blue-600"
              )} />
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className={cn(
                  "font-bold text-base truncate",
                  isActive("/dashboard/admin") ? "text-white" : "text-gray-900"
                )}>
                  Admin Dashboard
                </span>
              </div>
              <p className={cn(
                "text-sm truncate font-medium",
                isActive("/dashboard/admin") ? "text-blue-100" : "text-gray-500"
              )}>
                Overview, Analytics & Reports
              </p>
            </div>
          </Link>
        </div>

        {/* MoU Management Section */}
        <div className="mb-6">
          <h3 className="mb-4 flex items-center gap-3 px-3 text-sm font-bold uppercase tracking-wider text-gray-700">
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-green-100">
              <FileText className="h-4 w-4 text-green-600" />
            </div>
            MoU Management
          </h3>
          <div className="space-y-3">
            {/* Active MoUs */}
            <Collapsible
              open={openSections.includes('mou-list')}
              onOpenChange={() => toggleSection('mou-list')}
              className="w-full"
            >
              <CollapsibleTrigger className="flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-green-100">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="font-bold text-base">Active MoUs</span>
                    <span className="text-sm text-gray-500 font-medium">Manage active agreements</span>
                  </div>
                  <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs px-2 py-1 ml-auto mr-2 font-semibold">
                    45
                  </Badge>
                </div>
                <ChevronRight className={cn(
                  "h-4 w-4 transition-transform text-gray-400",
                  openSections.includes('mou-list') && "rotate-90"
                )} />
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 space-y-1 pl-16">
                  <Link
                    href="/dashboard/mou/active"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/mou/active")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    View Active MoUs
                  </Link>
                  <Link
                    href="/dashboard/mou/expired"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/mou/expired")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    View Expired MoUs
                  </Link>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Applications */}
            <Collapsible
              open={openSections.includes('applications')}
              onOpenChange={() => toggleSection('applications')}
              className="w-full"
            >
              <CollapsibleTrigger className="flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-amber-100">
                    <Clock className="h-6 w-6 text-amber-600" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="font-bold text-base">Applications</span>
                    <span className="text-sm text-gray-500 font-medium">Review & approve</span>
                  </div>
                  <Badge variant="secondary" className="bg-amber-100 text-amber-700 text-xs px-2 py-1 ml-auto mr-2 font-semibold">
                    12
                  </Badge>
                </div>
                <ChevronRight className={cn(
                  "h-4 w-4 transition-transform text-gray-400",
                  openSections.includes('applications') && "rotate-90"
                )} />
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 space-y-1 pl-16">
                  <Link
                    href="/dashboard/applications/pending"
                    className={cn(
                      "flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/applications/pending")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    <span>Pending Review</span>
                    <Badge variant="outline" className="bg-amber-50 text-amber-600 border-amber-200 text-xs">8</Badge>
                  </Link>
                  <Link
                    href="/dashboard/applications/review"
                    className={cn(
                      "flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/applications/review")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    <span>In Review</span>
                    <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 text-xs">4</Badge>
                  </Link>
                  <Link
                    href="/dashboard/applications/draft"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/applications/draft")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Draft Applications
                  </Link>
                  <Link
                    href="/dashboard/applications/rejected"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/applications/rejected")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Rejected Applications
                  </Link>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </div>

        {/* System Configuration */}
        <div className="mb-6">
          <h3 className="mb-4 flex items-center gap-3 px-3 text-sm font-bold uppercase tracking-wider text-gray-700">
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-purple-100">
              <Settings className="h-4 w-4 text-purple-600" />
            </div>
            System Configuration
          </h3>
          <div className="space-y-3">
            {/* Organizations */}
            <Collapsible
              open={openSections.includes('organization')}
              onOpenChange={() => toggleSection('organization')}
              className="w-full"
            >
              <CollapsibleTrigger className="flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-purple-100">
                    <Building2 className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="font-bold text-base">Organizations</span>
                    <span className="text-sm text-gray-500 font-medium">Setup & types</span>
                  </div>
                </div>
                <ChevronRight className={cn(
                  "h-4 w-4 transition-transform text-gray-400",
                  openSections.includes('organization') && "rotate-90"
                )} />
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 space-y-1 pl-16">
                  <Link
                    href="/dashboard/domain-interventions"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/domain-interventions")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Domain Interventions
                  </Link>
                  <Link
                    href="/dashboard/organization-types"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/organization-types")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Organization Types
                  </Link>
                  <Link
                    href="/dashboard/healthcare-providers"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/healthcare-providers")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Healthcare Providers
                  </Link>
                  <Link
                    href="/dashboard/input-categories"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/input-categories")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Input Categories
                  </Link>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Financial Setup */}
            <Collapsible
              open={openSections.includes('financial')}
              onOpenChange={() => toggleSection('financial')}
              className="w-full"
            >
              <CollapsibleTrigger className="flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50">
                <div className="flex items-center gap-3">
                  <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-emerald-100">
                    <DollarSign className="h-6 w-6 text-emerald-600" />
                  </div>
                  <div className="flex flex-col items-start">
                    <span className="font-bold text-base">Financial Setup</span>
                    <span className="text-sm text-gray-500 font-medium">Budget & funding</span>
                  </div>
                </div>
                <ChevronRight className={cn(
                  "h-4 w-4 transition-transform text-gray-400",
                  openSections.includes('financial') && "rotate-90"
                )} />
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 space-y-1 pl-16">
                  <Link
                    href="/dashboard/budget-types"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/budget-types")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Budget Types
                  </Link>
                  <Link
                    href="/dashboard/funding-source"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/funding-source")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Funding Sources
                  </Link>
                  <Link
                    href="/dashboard/funding-units"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/funding-units")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Funding Units
                  </Link>
                  <Link
                    href="/dashboard/financing-schemes"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/financing-schemes")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Financing Schemes
                  </Link>
                  <Link
                    href="/dashboard/financing-agents"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/financing-agents")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Financing Agents
                  </Link>
                  <Link
                    href="/dashboard/currency"
                    className={cn(
                      "flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors",
                      isActive("/dashboard/currency")
                        ? "bg-blue-100 text-blue-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100 hover:text-gray-700"
                    )}
                  >
                    Currencies
                  </Link>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </div>

        {/* User Management */}
        <div className="mb-6">
          <h3 className="mb-4 flex items-center gap-3 px-3 text-sm font-bold uppercase tracking-wider text-gray-700">
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-indigo-100">
              <Users className="h-4 w-4 text-indigo-600" />
            </div>
            User Management
          </h3>
          <div className="space-y-3">
            <Link
              href="/dashboard/users"
              className={cn(
                "group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg",
                isActive("/dashboard/users")
                  ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg"
                  : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200"
              )}
            >
              <div className={cn(
                "flex h-12 w-12 items-center justify-center rounded-xl transition-colors",
                isActive("/dashboard/users") ? "bg-white/20" : "bg-indigo-100"
              )}>
                <Users className={cn(
                  "h-6 w-6 transition-colors",
                  isActive("/dashboard/users") ? "text-white" : "text-indigo-600"
                )} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className={cn(
                    "font-bold text-base truncate",
                    isActive("/dashboard/users") ? "text-white" : "text-gray-900"
                  )}>
                    Manage Users
                  </span>
                </div>
                <p className={cn(
                  "text-sm truncate font-medium",
                  isActive("/dashboard/users") ? "text-blue-100" : "text-gray-500"
                )}>
                  User accounts & permissions
                </p>
              </div>
            </Link>

            <Link
              href="/dashboard/profile"
              className={cn(
                "group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg",
                isActive("/dashboard/profile")
                  ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg"
                  : "bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200"
              )}
            >
              <div className={cn(
                "flex h-12 w-12 items-center justify-center rounded-xl transition-colors",
                isActive("/dashboard/profile") ? "bg-white/20" : "bg-gray-100"
              )}>
                <UserCircle className={cn(
                  "h-6 w-6 transition-colors",
                  isActive("/dashboard/profile") ? "text-white" : "text-gray-600"
                )} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className={cn(
                    "font-bold text-base truncate",
                    isActive("/dashboard/profile") ? "text-white" : "text-gray-900"
                  )}>
                    My Profile
                  </span>
                </div>
                <p className={cn(
                  "text-sm truncate font-medium",
                  isActive("/dashboard/profile") ? "text-blue-100" : "text-gray-500"
                )}>
                  Personal settings & info
                </p>
              </div>
            </Link>
          </div>
        </div>
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-200 bg-white">
        <div className="p-5">
          <div className="flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-100 shadow-sm">
            <Avatar className="h-12 w-12 ring-2 ring-blue-100">
              <AvatarImage src="/placeholder.svg" alt={user?.firstName} />
              <AvatarFallback className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold text-lg">
                {user?.firstName?.[0]}
                {user?.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-base font-bold text-gray-900 truncate">
                  {user?.firstName} {user?.lastName}
                </span>
                <Badge
                  variant="secondary"
                  className={cn(
                    "text-xs px-2 py-1 font-semibold",
                    user?.role === 'ADMIN'
                      ? "bg-red-100 text-red-700"
                      : "bg-blue-100 text-blue-700"
                  )}
                >
                  {user?.role}
                </Badge>
              </div>
              <p className="text-sm text-gray-600 truncate font-medium">
                {user?.email}
              </p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => logout()}
              className="rounded-xl p-2 hover:bg-red-50 hover:text-red-600 transition-colors h-10 w-10"
              title="Logout"
            >
              <LogOut className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
