"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/partner/applications/new/page",{

/***/ "(app-pages-browser)/./components/wizard-steps/new-projects-step.tsx":
/*!*******************************************************!*\
  !*** ./components/wizard-steps/new-projects-step.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewProjectsStep: () => (/* binding */ NewProjectsStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/.pnpm/@hookform+resolvers@3.3.4_r_ffdcfe076cdc90c8a3b00ad082065b78/node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/.pnpm/react-hook-form@7.56.4_react@19.1.0/node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-auto-save */ \"(app-pages-browser)/./hooks/use-auto-save.ts\");\n/* harmony import */ var _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/store/mou-application-store */ \"(app-pages-browser)/./store/mou-application-store.ts\");\n/* harmony import */ var _lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/validations/mou-application */ \"(app-pages-browser)/./lib/validations/mou-application.ts\");\n/* harmony import */ var _hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-dropdown-data */ \"(app-pages-browser)/./hooks/use-dropdown-data.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calculator,FolderOpen,Info,Loader2,Plus,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ NewProjectsStep auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewProjectsStep() {\n    _s();\n    const { onBlur } = (0,_hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave)();\n    const { data, addProject, removeProject, updateProject } = (0,_store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore)();\n    const { data: dropdownData, loading: dropdownLoading, error: dropdownError } = (0,_hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__.useDropdownData)();\n    const { control, register, handleSubmit, watch, setValue, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_lib_validations_mou_application__WEBPACK_IMPORTED_MODULE_11__.projectsSchema),\n        defaultValues: {\n            projects: data.projects.length > 0 ? data.projects : []\n        }\n    });\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useFieldArray)({\n        control,\n        name: \"projects\"\n    });\n    const projects = watch('projects');\n    // Add new project\n    const handleAddProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[handleAddProject]\": ()=>{\n            const newProject = {\n                name: '',\n                fundingSourceId: '',\n                fundingUnitId: '',\n                budgetTypeId: '',\n                currencyId: '',\n                startDate: '',\n                endDate: '',\n                fiscalYearBudgets: [\n                    {\n                        fiscalYear: '2024-2025',\n                        budget: 1000\n                    }\n                ],\n                goals: [\n                    {\n                        description: 'Project goal description',\n                        isOverallGoal: true\n                    }\n                ]\n            };\n            append(newProject);\n            addProject(newProject);\n        }\n    }[\"NewProjectsStep.useCallback[handleAddProject]\"], [\n        append,\n        addProject\n    ]);\n    // Remove project\n    const handleRemoveProject = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[handleRemoveProject]\": (index)=>{\n            var _data_projects_index;\n            const projectId = (_data_projects_index = data.projects[index]) === null || _data_projects_index === void 0 ? void 0 : _data_projects_index.id;\n            if (projectId) {\n                removeProject(projectId);\n            }\n            remove(index);\n        }\n    }[\"NewProjectsStep.useCallback[handleRemoveProject]\"], [\n        remove,\n        removeProject,\n        data.projects\n    ]);\n    // Calculate total budget for a project\n    const calculateTotalBudget = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[calculateTotalBudget]\": (projectIndex)=>{\n            const project = projects[projectIndex];\n            if (!(project === null || project === void 0 ? void 0 : project.fiscalYearBudgets)) return 0;\n            return project.fiscalYearBudgets.reduce({\n                \"NewProjectsStep.useCallback[calculateTotalBudget]\": (sum, fy)=>sum + (fy.budget || 0)\n            }[\"NewProjectsStep.useCallback[calculateTotalBudget]\"], 0);\n        }\n    }[\"NewProjectsStep.useCallback[calculateTotalBudget]\"], [\n        projects\n    ]);\n    // Update project in store when form changes\n    const handleProjectUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NewProjectsStep.useCallback[handleProjectUpdate]\": (index, field, value)=>{\n            var _data_projects_index;\n            const projectId = (_data_projects_index = data.projects[index]) === null || _data_projects_index === void 0 ? void 0 : _data_projects_index.id;\n            if (projectId) {\n                updateProject(projectId, {\n                    [field]: value\n                });\n            }\n        }\n    }[\"NewProjectsStep.useCallback[handleProjectUpdate]\"], [\n        updateProject,\n        data.projects\n    ]);\n    // Show loading state while fetching dropdown data\n    if (dropdownLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"flex items-center justify-center py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Loading project data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    // Show error state if dropdown data failed to load\n    if (dropdownError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: \"Failed to load project configuration data. Please refresh the page and try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this),\n                                \"Project Information\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                            children: \"Define projects that will be covered under this MoU\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: fields.map((field, index)=>{\n                    var _errors_projects_index, _errors_projects, _errors_projects_index_name, _errors_projects_index1, _errors_projects_index2, _errors_projects1, _errors_projects_index_fundingSourceId, _errors_projects_index3, _errors_projects_index4, _errors_projects2, _errors_projects_index_fundingUnitId, _errors_projects_index5, _errors_projects_index6, _errors_projects3, _errors_projects_index_budgetTypeId, _errors_projects_index7, _errors_projects_index8, _errors_projects4, _errors_projects_index_currencyId, _errors_projects_index9, _errors_projects_index10, _errors_projects5, _errors_projects_index_startDate, _errors_projects_index11, _errors_projects_index12, _errors_projects6, _errors_projects_index_endDate, _errors_projects_index13, _projects_index_fiscalYearBudgets, _projects_index, _projects_index_goals, _projects_index1, _projects_index2, _dropdownData_currencies_find;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                className: \"pb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: [\n                                                \"Project \",\n                                                index + 1\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>handleRemoveProject(index),\n                                            className: \"text-destructive hover:text-destructive\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Remove\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".name\"),\n                                                        children: [\n                                                            \"Project Name \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"projects.\".concat(index, \".name\"),\n                                                        ...register(\"projects.\".concat(index, \".name\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleProjectUpdate(index, 'name', e.target.value);\n                                                            }\n                                                        }),\n                                                        placeholder: \"Enter project name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects = errors.projects) === null || _errors_projects === void 0 ? void 0 : (_errors_projects_index = _errors_projects[index]) === null || _errors_projects_index === void 0 ? void 0 : _errors_projects_index.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index1 = errors.projects[index]) === null || _errors_projects_index1 === void 0 ? void 0 : (_errors_projects_index_name = _errors_projects_index1.name) === null || _errors_projects_index_name === void 0 ? void 0 : _errors_projects_index_name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".fundingSourceId\"),\n                                                        children: [\n                                                            \"Funding Source \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 36\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".fundingSourceId\"), value);\n                                                            handleProjectUpdate(index, 'fundingSourceId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select funding source\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.fundingSources.map((source)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: source.id,\n                                                                        children: source.name\n                                                                    }, source.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects1 = errors.projects) === null || _errors_projects1 === void 0 ? void 0 : (_errors_projects_index2 = _errors_projects1[index]) === null || _errors_projects_index2 === void 0 ? void 0 : _errors_projects_index2.fundingSourceId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index3 = errors.projects[index]) === null || _errors_projects_index3 === void 0 ? void 0 : (_errors_projects_index_fundingSourceId = _errors_projects_index3.fundingSourceId) === null || _errors_projects_index_fundingSourceId === void 0 ? void 0 : _errors_projects_index_fundingSourceId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".fundingUnitId\"),\n                                                        children: [\n                                                            \"Funding Unit \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 34\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".fundingUnitId\"), value);\n                                                            handleProjectUpdate(index, 'fundingUnitId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select funding unit\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.fundingUnits.map((unit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: unit.id,\n                                                                        children: unit.name\n                                                                    }, unit.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 234,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects2 = errors.projects) === null || _errors_projects2 === void 0 ? void 0 : (_errors_projects_index4 = _errors_projects2[index]) === null || _errors_projects_index4 === void 0 ? void 0 : _errors_projects_index4.fundingUnitId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index5 = errors.projects[index]) === null || _errors_projects_index5 === void 0 ? void 0 : (_errors_projects_index_fundingUnitId = _errors_projects_index5.fundingUnitId) === null || _errors_projects_index_fundingUnitId === void 0 ? void 0 : _errors_projects_index_fundingUnitId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".budgetTypeId\"),\n                                                        children: [\n                                                            \"Budget Type \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".budgetTypeId\"), value);\n                                                            handleProjectUpdate(index, 'budgetTypeId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select budget type\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.budgetTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: type.id,\n                                                                        children: type.name\n                                                                    }, type.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects3 = errors.projects) === null || _errors_projects3 === void 0 ? void 0 : (_errors_projects_index6 = _errors_projects3[index]) === null || _errors_projects_index6 === void 0 ? void 0 : _errors_projects_index6.budgetTypeId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index7 = errors.projects[index]) === null || _errors_projects_index7 === void 0 ? void 0 : (_errors_projects_index_budgetTypeId = _errors_projects_index7.budgetTypeId) === null || _errors_projects_index_budgetTypeId === void 0 ? void 0 : _errors_projects_index_budgetTypeId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".currencyId\"),\n                                                        children: [\n                                                            \"Currency \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 30\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                        onValueChange: (value)=>{\n                                                            setValue(\"projects.\".concat(index, \".currencyId\"), value);\n                                                            handleProjectUpdate(index, 'currencyId', value);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                    placeholder: \"Select currency\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 288,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                children: dropdownData.currencies.map((currency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                        value: currency.id,\n                                                                        children: [\n                                                                            currency.code,\n                                                                            \" - \",\n                                                                            currency.name\n                                                                        ]\n                                                                    }, currency.id, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 292,\n                                                                        columnNumber: 25\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects4 = errors.projects) === null || _errors_projects4 === void 0 ? void 0 : (_errors_projects_index8 = _errors_projects4[index]) === null || _errors_projects_index8 === void 0 ? void 0 : _errors_projects_index8.currencyId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index9 = errors.projects[index]) === null || _errors_projects_index9 === void 0 ? void 0 : (_errors_projects_index_currencyId = _errors_projects_index9.currencyId) === null || _errors_projects_index_currencyId === void 0 ? void 0 : _errors_projects_index_currencyId.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".startDate\"),\n                                                        children: [\n                                                            \"Start Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"projects.\".concat(index, \".startDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"projects.\".concat(index, \".startDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleProjectUpdate(index, 'startDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects5 = errors.projects) === null || _errors_projects5 === void 0 ? void 0 : (_errors_projects_index10 = _errors_projects5[index]) === null || _errors_projects_index10 === void 0 ? void 0 : _errors_projects_index10.startDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index11 = errors.projects[index]) === null || _errors_projects_index11 === void 0 ? void 0 : (_errors_projects_index_startDate = _errors_projects_index11.startDate) === null || _errors_projects_index_startDate === void 0 ? void 0 : _errors_projects_index_startDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"projects.\".concat(index, \".endDate\"),\n                                                        children: [\n                                                            \"End Date \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 30\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"projects.\".concat(index, \".endDate\"),\n                                                        type: \"date\",\n                                                        ...register(\"projects.\".concat(index, \".endDate\"), {\n                                                            onBlur: (e)=>{\n                                                                onBlur(e);\n                                                                handleProjectUpdate(index, 'endDate', e.target.value);\n                                                            }\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ((_errors_projects6 = errors.projects) === null || _errors_projects6 === void 0 ? void 0 : (_errors_projects_index12 = _errors_projects6[index]) === null || _errors_projects_index12 === void 0 ? void 0 : _errors_projects_index12.endDate) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-destructive\",\n                                                        children: (_errors_projects_index13 = errors.projects[index]) === null || _errors_projects_index13 === void 0 ? void 0 : (_errors_projects_index_endDate = _errors_projects_index13.endDate) === null || _errors_projects_index_endDate === void 0 ? void 0 : _errors_projects_index_endDate.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: [\n                                                            \"Fiscal Year Budgets \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 41\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _projects_index;\n                                                            const currentBudgets = ((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.fiscalYearBudgets) || [];\n                                                            const newBudget = {\n                                                                fiscalYear: '2025-2026',\n                                                                budget: 1000\n                                                            };\n                                                            setValue(\"projects.\".concat(index, \".fiscalYearBudgets\"), [\n                                                                ...currentBudgets,\n                                                                newBudget\n                                                            ]);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Fiscal Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            (_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : (_projects_index_fiscalYearBudgets = _projects_index.fiscalYearBudgets) === null || _projects_index_fiscalYearBudgets === void 0 ? void 0 : _projects_index_fiscalYearBudgets.map((fyBudget, fyIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Fiscal Year\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                                    onValueChange: (value)=>{\n                                                                        var _projects_index;\n                                                                        const currentBudgets = [\n                                                                            ...((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.fiscalYearBudgets) || []\n                                                                        ];\n                                                                        currentBudgets[fyIndex] = {\n                                                                            ...currentBudgets[fyIndex],\n                                                                            fiscalYear: value\n                                                                        };\n                                                                        setValue(\"projects.\".concat(index, \".fiscalYearBudgets\"), currentBudgets);\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                                placeholder: \"Select fiscal year\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                                lineNumber: 387,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                            lineNumber: 386,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                            children: dropdownData.fiscalYears.map((fy)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                                    value: fy.year,\n                                                                                    children: fy.year\n                                                                                }, fy.id, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                                    lineNumber: 391,\n                                                                                    columnNumber: 29\n                                                                                }, this))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                            lineNumber: 389,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    children: \"Budget Amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    type: \"number\",\n                                                                    min: \"0\",\n                                                                    step: \"0.01\",\n                                                                    ...register(\"projects.\".concat(index, \".fiscalYearBudgets.\").concat(fyIndex, \".budget\"), {\n                                                                        valueAsNumber: true,\n                                                                        onBlur: onBlur\n                                                                    }),\n                                                                    placeholder: \"Enter budget amount\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 399,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                type: \"button\",\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>{\n                                                                    var _projects_index;\n                                                                    const currentBudgets = [\n                                                                        ...((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.fiscalYearBudgets) || []\n                                                                    ];\n                                                                    currentBudgets.splice(fyIndex, 1);\n                                                                    setValue(\"projects.\".concat(index, \".fiscalYearBudgets\"), currentBudgets);\n                                                                },\n                                                                className: \"text-destructive hover:text-destructive\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 413,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, fyIndex, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-base font-medium\",\n                                                        children: [\n                                                            \"Project Goals \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-destructive\",\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>{\n                                                            var _projects_index;\n                                                            const currentGoals = ((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.goals) || [];\n                                                            const newGoal = {\n                                                                description: '',\n                                                                isOverallGoal: false\n                                                            };\n                                                            setValue(\"projects.\".concat(index, \".goals\"), [\n                                                                ...currentGoals,\n                                                                newGoal\n                                                            ]);\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Add Goal\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 17\n                                            }, this),\n                                            (_projects_index1 = projects[index]) === null || _projects_index1 === void 0 ? void 0 : (_projects_index_goals = _projects_index1.goals) === null || _projects_index_goals === void 0 ? void 0 : _projects_index_goals.map((goal, goalIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: goal.isOverallGoal ? 'Overall Goal' : \"Additional Goal \".concat(goalIndex)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                !goal.isOverallGoal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>{\n                                                                        var _projects_index;\n                                                                        const currentGoals = [\n                                                                            ...((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.goals) || []\n                                                                        ];\n                                                                        currentGoals.splice(goalIndex, 1);\n                                                                        setValue(\"projects.\".concat(index, \".goals\"), currentGoals);\n                                                                    },\n                                                                    className: \"text-destructive hover:text-destructive\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                        lineNumber: 474,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            ...register(\"projects.\".concat(index, \".goals.\").concat(goalIndex, \".description\"), {\n                                                                onBlur: onBlur\n                                                            }),\n                                                            placeholder: goal.isOverallGoal ? \"Enter the overall goal for this project...\" : \"Enter additional goal...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, goalIndex, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-muted p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Total Project Budget\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-primary\",\n                                                children: [\n                                                    calculateTotalBudget(index).toLocaleString(),\n                                                    ((_projects_index2 = projects[index]) === null || _projects_index2 === void 0 ? void 0 : _projects_index2.currencyId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-normal text-muted-foreground ml-2\",\n                                                        children: (_dropdownData_currencies_find = dropdownData.currencies.find((c)=>{\n                                                            var _projects_index;\n                                                            return c.id === ((_projects_index = projects[index]) === null || _projects_index === void 0 ? void 0 : _projects_index.currencyId);\n                                                        })) === null || _dropdownData_currencies_find === void 0 ? void 0 : _dropdownData_currencies_find.code\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Auto-calculated from fiscal year budgets above\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, field.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 144,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"border-dashed\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"flex flex-col items-center justify-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            type: \"button\",\n                            variant: \"outline\",\n                            onClick: handleAddProject,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 13\n                                }, this),\n                                \"Add Project\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-muted-foreground mt-2\",\n                            children: \"Add multiple projects that will be covered under this MoU\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 512,\n                columnNumber: 7\n            }, this),\n            fields.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                variant: \"destructive\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 532,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: \"At least one project must be added to proceed to the next step.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 533,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 531,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calculator_FolderOpen_Info_Loader2_Plus_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Note:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this),\n                            \" Each project should represent a distinct initiative or program that will be implemented under this MoU. Ensure all financial and timeline information is accurate.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                        lineNumber: 542,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n                lineNumber: 540,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\wizard-steps\\\\new-projects-step.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(NewProjectsStep, \"XIhJpzVeQUPp1d+YcyrP7sX+O+s=\", false, function() {\n    return [\n        _hooks_use_auto_save__WEBPACK_IMPORTED_MODULE_9__.useFieldBlurAutoSave,\n        _store_mou_application_store__WEBPACK_IMPORTED_MODULE_10__.useMouApplicationStore,\n        _hooks_use_dropdown_data__WEBPACK_IMPORTED_MODULE_12__.useDropdownData,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_13__.useFieldArray\n    ];\n});\n_c = NewProjectsStep;\nvar _c;\n$RefreshReg$(_c, \"NewProjectsStep\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/wizard-steps/new-projects-step.tsx\n"));

/***/ })

});