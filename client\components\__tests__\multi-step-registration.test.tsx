import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { MultiStepRegistration } from '../multi-step-registration'

// Mock the auth context
jest.mock('@/contexts/auth-context', () => ({
  useAuth: () => ({
    register: jest.fn()
  })
}))

// Mock the master data service
jest.mock('@/lib/services/master-data.service', () => ({
  masterDataService: {
    getOrganizationTypes: jest.fn().mockResolvedValue([
      { id: 1, typeName: 'NGO' },
      { id: 2, typeName: 'Government' }
    ])
  }
}))

// Mock fetch for countries API
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve([
      { name: { common: 'Rwanda' }, cca2: 'RW', cca3: 'RWA' },
      { name: { common: 'Kenya' }, cca2: 'KE', cca3: 'KEN' },
      { name: { common: 'Uganda' }, cca2: 'UG', cca3: 'UGA' }
    ])
  })
) as jest.Mock

describe('MultiStepRegistration Performance', () => {
  const mockOnSuccess = jest.fn()
  const mockOnCancel = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('should have Rwanda as default country', async () => {
    render(
      <MultiStepRegistration 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />
    )

    // Navigate to step 2
    fireEvent.click(screen.getByText('Next'))

    // Wait for countries to load and check if Rwanda is selected by default
    await waitFor(() => {
      const countrySelect = screen.getByDisplayValue('Rwanda')
      expect(countrySelect).toBeInTheDocument()
    })
  })

  test('should show RGB number field for Rwanda (local organization)', async () => {
    render(
      <MultiStepRegistration 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />
    )

    // Navigate to step 2
    fireEvent.click(screen.getByText('Next'))

    // Wait for the form to load
    await waitFor(() => {
      const rgbField = screen.getByLabelText(/RGB Number/i)
      expect(rgbField).toBeInTheDocument()
      expect(rgbField).toBeRequired()
    })
  })

  test('should handle input changes without lag', async () => {
    render(
      <MultiStepRegistration 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />
    )

    // Navigate to step 2
    fireEvent.click(screen.getByText('Next'))

    await waitFor(() => {
      const orgNameInput = screen.getByLabelText(/Organization Name/i)
      
      // Simulate rapid typing
      const testValue = 'Test Organization Name'
      fireEvent.change(orgNameInput, { target: { value: testValue } })
      
      expect(orgNameInput).toHaveValue(testValue)
    })
  })

  test('should efficiently handle country selection changes', async () => {
    render(
      <MultiStepRegistration 
        onSuccess={mockOnSuccess} 
        onCancel={mockOnCancel} 
      />
    )

    // Navigate to step 2
    fireEvent.click(screen.getByText('Next'))

    await waitFor(() => {
      // Initially Rwanda should be selected and RGB field should be visible
      expect(screen.getByDisplayValue('Rwanda')).toBeInTheDocument()
      expect(screen.getByLabelText(/RGB Number/i)).toBeInTheDocument()
    })

    // Change to international country
    const countrySelect = screen.getByRole('combobox')
    fireEvent.click(countrySelect)
    
    await waitFor(() => {
      const kenyaOption = screen.getByText('Kenya')
      fireEvent.click(kenyaOption)
    })

    // RGB field should be hidden for international organizations
    await waitFor(() => {
      expect(screen.queryByLabelText(/RGB Number/i)).not.toBeInTheDocument()
    })
  })
})
