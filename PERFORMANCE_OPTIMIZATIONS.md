# Multi-Step Registration Performance Optimizations

## Overview
This document outlines the performance optimizations implemented in the `multi-step-registration.tsx` component to resolve input lag issues in Step 2 (Organization Information) and improve overall user experience.

## Issues Identified and Fixed

### 1. **Expensive Function Calls on Every Render**
**Problem**: The `isLocalOrganization()` function was called multiple times on every render, causing unnecessary re-computations.

**Solution**: Memoized the function using `useMemo`:
```typescript
const isLocalOrganization = useMemo(() => {
  return formData.organizationCountry.toLowerCase() === 'rwanda'
}, [formData.organizationCountry])
```

### 2. **Inefficient Address Updates**
**Problem**: The `useEffect` for updating addresses triggered expensive state updates on every country change without checking if updates were actually needed.

**Solution**: Added conditional checks to prevent unnecessary updates:
```typescript
useEffect(() => {
  // Only update if there's actually a change to prevent unnecessary re-renders
  const currentAddr = prev.addresses[0]
  if (currentAddr?.addressType === newAddressType && currentAddr?.country === newCountry) {
    return prev
  }
  // ... update logic
}, [formData.organizationCountry, isLocalOrganization])
```

### 3. **Unoptimized Form Update Functions**
**Problem**: Form update functions were recreated on every render, causing child components to re-render unnecessarily.

**Solution**: Memoized update functions using `useCallback`:
```typescript
const updateFormData = useCallback((field: string, value: any) => {
  setFormData(prev => ({ ...prev, [field]: value }))
}, [])

const updateAddress = useCallback((index: number, field: string, value: string) => {
  setFormData(prev => ({
    ...prev,
    addresses: prev.addresses.map((addr, i) =>
      i === index ? { ...addr, [field]: value } : addr
    )
  }))
}, [])
```

### 4. **Expensive Countries Sorting**
**Problem**: Countries were sorted on every render, causing performance issues.

**Solution**: Memoized the sorted countries list:
```typescript
const sortedCountries = useMemo(() => {
  return countries.sort((a, b) => {
    // Put Rwanda first, then sort alphabetically
    if (a.name.common === "Rwanda") return -1
    if (b.name.common === "Rwanda") return 1
    return a.name.common.localeCompare(b.name.common)
  })
}, [countries])
```

### 5. **Unoptimized Validation Function**
**Problem**: The validation function was recreated on every render.

**Solution**: Memoized using `useCallback`:
```typescript
const validateStep = useCallback((step: number): boolean => {
  // ... validation logic
}, [formData, isLocalOrganization])
```

### 6. **Unoptimized Navigation Functions**
**Problem**: Navigation functions were recreated on every render.

**Solution**: Memoized using `useCallback`:
```typescript
const handleNext = useCallback(() => {
  if (validateStep(currentStep)) {
    setCurrentStep(prev => prev + 1)
  }
}, [validateStep, currentStep])

const handlePrevious = useCallback(() => {
  setCurrentStep(prev => prev - 1)
}, [])
```

### 7. **Step Indicator Re-rendering**
**Problem**: Step indicator was recreated on every render.

**Solution**: Memoized the step indicator:
```typescript
const renderStepIndicator = useMemo(() => (
  // ... step indicator JSX
), [currentStep])
```

## Default Country Selection

### **Rwanda as Default**
**Implementation**: Set Rwanda as the default selected country in the initial form state:
```typescript
const [formData, setFormData] = useState<RegistrationData>({
  // ... other fields
  organizationCountry: "Rwanda", // Default to Rwanda
  addresses: [
    {
      addressType: "HEADQUARTERS",
      country: "Rwanda", // Default to Rwanda
      street: "",
      poBox: "",
    }
  ]
})
```

**Benefits**:
- Streamlines registration for local organizations (majority use case)
- Automatically shows RGB number field for local organizations
- Proper address labeling from the start
- Rwanda appears first in the country dropdown

## Performance Improvements Achieved

1. **Reduced Re-renders**: Memoization prevents unnecessary component re-renders
2. **Faster Input Response**: Optimized update functions eliminate input lag
3. **Efficient Country Loading**: Countries are sorted once and cached
4. **Optimized Validation**: Validation logic is memoized and only runs when dependencies change
5. **Better Memory Usage**: Functions are not recreated on every render

## Testing

Created comprehensive tests in `__tests__/multi-step-registration.test.tsx` to verify:
- Rwanda is set as default country
- RGB number field appears for local organizations
- Input changes are handled efficiently
- Country selection changes work smoothly

## Usage Notes

- The component now defaults to Rwanda, making it ideal for local organizations
- International organizations can easily change the country selection
- All performance optimizations are backward compatible
- The component maintains the same API and functionality

## Future Considerations

1. Consider implementing virtual scrolling for very large country lists
2. Add debouncing for search functionality if country search is added
3. Consider lazy loading of organization types if the list becomes large
4. Monitor bundle size impact of memoization hooks
