// Mock data for dropdowns and selections

export const mockFundingSources = [
  { id: "1", name: "Government" },
  { id: "2", name: "Private Sector" },
  { id: "3", name: "International Donors" },
  { id: "4", name: "NGOs" },
  { id: "5", name: "World Bank" },
  { id: "6", name: "African Development Bank" },
];

export const mockFundingUnits = [
  { id: "1", name: "Ministry of Health" },
  { id: "2", name: "Rwanda Biomedical Centre" },
  { id: "3", name: "University Teaching Hospital" },
  { id: "4", name: "District Hospitals" },
  { id: "5", name: "Health Centers" },
];

export const mockBudgetTypes = [
  { id: "1", name: "Operational" },
  { id: "2", name: "Capital" },
  { id: "3", name: "Project-based" },
  { id: "4", name: "Emergency" },
  { id: "5", name: "Development" },
];

export const mockCurrencies = [
  { id: "USD", code: "USD", name: "US Dollar", symbol: "$" },
  { id: "EUR", code: "EUR", name: "Euro", symbol: "€" },
  { id: "RWF", code: "RWF", name: "Rwandan Franc", symbol: "RWF" },
  { id: "GBP", code: "GBP", name: "British Pound", symbol: "£" },
];

// Generate fiscal years dynamically based on current year
const getCurrentFiscalYears = () => {
  const currentYear = new Date().getFullYear();
  const currentFiscalYear = `${currentYear}-${currentYear + 1}`;
  const nextFiscalYear = `${currentYear + 1}-${currentYear + 2}`;

  return [
    { id: currentFiscalYear, year: currentFiscalYear, name: currentFiscalYear },
    { id: nextFiscalYear, year: nextFiscalYear, name: nextFiscalYear }
  ];
};

export const mockFiscalYears = getCurrentFiscalYears();

// Domain Interventions with cascading structure
export const mockDomains = [
  {
    id: "1",
    name: "Health Service Delivery",
    subDomains: [
      {
        id: "1-1",
        name: "Primary Healthcare",
        functions: [
          {
            id: "1-1-1",
            name: "Preventive Care",
            subFunctions: [
              { id: "1-1-1-1", name: "Vaccination Programs" },
              { id: "1-1-1-2", name: "Health Screening" },
              { id: "1-1-1-3", name: "Health Education" },
            ]
          },
          {
            id: "1-1-2",
            name: "Curative Care",
            subFunctions: [
              { id: "1-1-2-1", name: "Outpatient Services" },
              { id: "1-1-2-2", name: "Emergency Care" },
              { id: "1-1-2-3", name: "Chronic Disease Management" },
            ]
          }
        ]
      },
      {
        id: "1-2",
        name: "Secondary Healthcare",
        functions: [
          {
            id: "1-2-1",
            name: "Specialized Services",
            subFunctions: [
              { id: "1-2-1-1", name: "Surgery" },
              { id: "1-2-1-2", name: "Diagnostics" },
              { id: "1-2-1-3", name: "Rehabilitation" },
            ]
          }
        ]
      }
    ]
  },
  {
    id: "2",
    name: "Health Workforce",
    subDomains: [
      {
        id: "2-1",
        name: "Training & Development",
        functions: [
          {
            id: "2-1-1",
            name: "Professional Training",
            subFunctions: [
              { id: "2-1-1-1", name: "Medical Education" },
              { id: "2-1-1-2", name: "Nursing Education" },
              { id: "2-1-1-3", name: "Continuing Education" },
            ]
          }
        ]
      }
    ]
  },
  {
    id: "3",
    name: "Health Information Systems",
    subDomains: [
      {
        id: "3-1",
        name: "Data Management",
        functions: [
          {
            id: "3-1-1",
            name: "Health Records",
            subFunctions: [
              { id: "3-1-1-1", name: "Electronic Health Records" },
              { id: "3-1-1-2", name: "Patient Registration" },
              { id: "3-1-1-3", name: "Health Analytics" },
            ]
          }
        ]
      }
    ]
  }
];

// Input Categories with Activity Inputs
export const mockInputCategories = [
  {
    id: "1",
    name: "Human Resources",
    activityInputs: [
      { id: "1-1", name: "Medical Staff" },
      { id: "1-2", name: "Nursing Staff" },
      { id: "1-3", name: "Administrative Staff" },
      { id: "1-4", name: "Technical Staff" },
      { id: "1-5", name: "Support Staff" },
    ]
  },
  {
    id: "2",
    name: "Medical Equipment",
    activityInputs: [
      { id: "2-1", name: "Diagnostic Equipment" },
      { id: "2-2", name: "Treatment Equipment" },
      { id: "2-3", name: "Laboratory Equipment" },
      { id: "2-4", name: "Surgical Equipment" },
      { id: "2-5", name: "Emergency Equipment" },
    ]
  },
  {
    id: "3",
    name: "Pharmaceuticals",
    activityInputs: [
      { id: "3-1", name: "Essential Medicines" },
      { id: "3-2", name: "Vaccines" },
      { id: "3-3", name: "Medical Supplies" },
      { id: "3-4", name: "Laboratory Reagents" },
      { id: "3-5", name: "Contraceptives" },
    ]
  },
  {
    id: "4",
    name: "Infrastructure",
    activityInputs: [
      { id: "4-1", name: "Building Construction" },
      { id: "4-2", name: "Renovation" },
      { id: "4-3", name: "Utilities" },
      { id: "4-4", name: "Transportation" },
      { id: "4-5", name: "ICT Infrastructure" },
    ]
  },
  {
    id: "5",
    name: "Training & Capacity Building",
    activityInputs: [
      { id: "5-1", name: "Professional Development" },
      { id: "5-2", name: "Skills Training" },
      { id: "5-3", name: "Leadership Development" },
      { id: "5-4", name: "Technical Training" },
      { id: "5-5", name: "Community Health Training" },
    ]
  }
];

// Provinces with Districts
export const mockProvinces = [
  {
    id: "1",
    name: "Kigali City",
    districts: [
      { id: "1-1", name: "Gasabo" },
      { id: "1-2", name: "Kicukiro" },
      { id: "1-3", name: "Nyarugenge" },
    ]
  },
  {
    id: "2",
    name: "Eastern Province",
    districts: [
      { id: "2-1", name: "Bugesera" },
      { id: "2-2", name: "Gatsibo" },
      { id: "2-3", name: "Kayonza" },
      { id: "2-4", name: "Kirehe" },
      { id: "2-5", name: "Ngoma" },
      { id: "2-6", name: "Nyagatare" },
      { id: "2-7", name: "Rwamagana" },
    ]
  },
  {
    id: "3",
    name: "Northern Province",
    districts: [
      { id: "3-1", name: "Burera" },
      { id: "3-2", name: "Gakenke" },
      { id: "3-3", name: "Gicumbi" },
      { id: "3-4", name: "Musanze" },
      { id: "3-5", name: "Rulindo" },
    ]
  },
  {
    id: "4",
    name: "Southern Province",
    districts: [
      { id: "4-1", name: "Gisagara" },
      { id: "4-2", name: "Huye" },
      { id: "4-3", name: "Kamonyi" },
      { id: "4-4", name: "Muhanga" },
      { id: "4-5", name: "Nyamagabe" },
      { id: "4-6", name: "Nyanza" },
      { id: "4-7", name: "Nyaruguru" },
      { id: "4-8", name: "Ruhango" },
    ]
  },
  {
    id: "5",
    name: "Western Province",
    districts: [
      { id: "5-1", name: "Karongi" },
      { id: "5-2", name: "Ngororero" },
      { id: "5-3", name: "Nyabihu" },
      { id: "5-4", name: "Nyamasheke" },
      { id: "5-5", name: "Rubavu" },
      { id: "5-6", name: "Rusizi" },
      { id: "5-7", name: "Rutsiro" },
    ]
  }
];

// Central Levels
export const mockCentralLevels = [
  { id: "1", name: "Ministry of Health" },
  { id: "2", name: "Rwanda Biomedical Centre" },
  { id: "3", name: "University Teaching Hospital of Butare" },
  { id: "4", name: "University Teaching Hospital of Kigali" },
  { id: "5", name: "Rwanda Military Hospital" },
  { id: "6", name: "King Faisal Hospital" },
  { id: "7", name: "National Reference Laboratories" },
  { id: "8", name: "Rwanda Food and Drug Authority" },
  { id: "9", name: "Community Health Cooperatives" },
  { id: "10", name: "Health Development Initiative" },
];

// Document Types for Upload
export const documentTypes = [
  {
    id: "MEMO_OBJECTIVE",
    name: "Memo Describing The Long Term Objective",
    description: "Document outlining the long-term objectives of the MoU",
    required: true,
    order: 1
  },
  {
    id: "STRATEGIC_PLAN",
    name: "Strategic Plan",
    description: "Strategic plan document for the partnership",
    required: true,
    order: 2
  },
  {
    id: "CAPACITY_BUILDING",
    name: "Capacity Building Transfer Plan",
    description: "Plan for capacity building and knowledge transfer",
    required: true,
    order: 3
  },
  {
    id: "MEMO_FUNDS",
    name: "Memo Describing the Source of Funds",
    description: "Document describing funding sources and financial arrangements",
    required: true,
    order: 4
  }
];

// Accepted file types and size limits
export const fileUploadConfig = {
  maxSize: 10 * 1024 * 1024, // 10MB
  acceptedTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif'
  ],
  acceptedExtensions: ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.jpg', '.jpeg', '.png', '.gif']
};

// Demo organization data (for pre-filled fields)
export const demoOrganization = {
  id: 1,
  name: "Rwanda Health Partners Initiative",
  type: "International NGO",
  registrationNumber: "RHP-2024-001",
  contactPerson: "Dr. Sarah Johnson",
  email: "<EMAIL>",
  phone: "+250 788 123 456"
};
