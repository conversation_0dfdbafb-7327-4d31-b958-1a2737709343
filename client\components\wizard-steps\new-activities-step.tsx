"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldArray, useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useFieldBlurAutoSave } from '@/hooks/use-auto-save'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { activitiesSchema } from '@/lib/validations/mou-application'
import { useDropdownData } from '@/hooks/use-dropdown-data'
import { Trash2, Plus, Activity, AlertTriangle, Info, MapPin, Loader2 } from 'lucide-react'

type FormData = z.infer<typeof activitiesSchema>

export function NewActivitiesStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const { data, addActivity, removeActivity, updateActivity } = useMouApplicationStore()
  const { data: dropdownData, loading: dropdownLoading, error: dropdownError } = useDropdownData()

  // Filter fiscal years to only current and next year
  const filteredFiscalYears = dropdownData.fiscalYears.filter(fy =>
    fy.year === '2024-2025' || fy.year === '2025-2026'
  )

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(activitiesSchema),
    defaultValues: {
      activities: data.activities.length > 0 ? data.activities : []
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: "activities"
  })

  const activities = watch('activities')

  // Get available projects for activity assignment
  const availableProjects = data.projects.map(project => ({
    id: project.id,
    name: project.name
  }))

  // Add new activity
  const handleAddActivity = useCallback(() => {
    const newActivity = {
      projectId: '',
      name: '',
      implementor: '',
      implementingUnit: '',
      fiscalYear: '',
      startDate: '',
      endDate: '',
      domain: '',
      subDomain: '',
      subDomainFunction: '',
      subFunction: '',
      inputCategory: '',
      activityInput: '',
      geographicLevel: 'Provinces' as const,
      budgetAllocations: []
    }
    append(newActivity)
    addActivity(newActivity)
  }, [append, addActivity])

  // Remove activity
  const handleRemoveActivity = useCallback((index: number) => {
    const activityId = data.activities[index]?.id
    if (activityId) {
      removeActivity(activityId)
    }
    remove(index)
  }, [remove, removeActivity, data.activities])

  // Update activity in store when form changes
  const handleActivityUpdate = useCallback((index: number, field: string, value: any) => {
    const activityId = data.activities[index]?.id
    if (activityId) {
      updateActivity(activityId, { [field]: value })
    }
  }, [updateActivity, data.activities])

  // Get cascading dropdown options
  const getSubDomains = useCallback((domainId: string) => {
    const domain = dropdownData.domains.find(d => d.id === domainId)
    return domain?.subDomains || []
  }, [dropdownData.domains])

  const getDomainFunctions = useCallback((domainId: string, subDomainId: string) => {
    const domain = dropdownData.domains.find(d => d.id === domainId)
    const subDomain = domain?.subDomains.find(sd => sd.id === subDomainId)
    return subDomain?.functions || []
  }, [dropdownData.domains])

  const getSubFunctions = useCallback((domainId: string, subDomainId: string, functionId: string) => {
    const domain = dropdownData.domains.find(d => d.id === domainId)
    const subDomain = domain?.subDomains.find(sd => sd.id === subDomainId)
    const domainFunction = subDomain?.functions.find(f => f.id === functionId)
    return domainFunction?.subFunctions || []
  }, [dropdownData.domains])

  const getActivityInputs = useCallback((categoryId: string) => {
    const category = dropdownData.inputCategories.find(c => c.id === categoryId)
    return category?.inputs || []
  }, [dropdownData.inputCategories])

  const getDistricts = useCallback((provinceId: string) => {
    const province = dropdownData.provinces.find(p => p.id === provinceId)
    return province?.districts || []
  }, [dropdownData.provinces])

  // Show loading state while fetching dropdown data
  if (dropdownLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading activity configuration data...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state if dropdown data failed to load
  if (dropdownError) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load activity configuration data. Please refresh the page and try again.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Activity Details
          </CardTitle>
          <CardDescription>
            Create activities for each project with detailed implementation information
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Check if projects exist */}
      {availableProjects.length === 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            No projects available. Please add at least one project in the previous step before creating activities.
          </AlertDescription>
        </Alert>
      )}

      {/* Activities List */}
      {availableProjects.length > 0 && (
        <div className="space-y-6">
          {fields.map((field, index) => (
            <Card key={field.id} className="relative">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    Activity {index + 1}
                  </CardTitle>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveActivity(index)}
                    className="text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                    Remove
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Basic Activity Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Project Assignment */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.projectId`}>
                      Assign to Project <span className="text-destructive">*</span>
                    </Label>
                    <Select
                      onValueChange={(value) => {
                        setValue(`activities.${index}.projectId`, value)
                        handleActivityUpdate(index, 'projectId', value)
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select project" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableProjects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.activities?.[index]?.projectId && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.projectId?.message}
                      </p>
                    )}
                  </div>

                  {/* Activity Name */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.name`}>
                      Activity Name <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id={`activities.${index}.name`}
                      {...register(`activities.${index}.name`, {
                        onBlur: (e) => {
                          onBlur(e)
                          handleActivityUpdate(index, 'name', e.target.value)
                        }
                      })}
                      placeholder="Enter activity name"
                    />
                    {errors.activities?.[index]?.name && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.name?.message}
                      </p>
                    )}
                  </div>

                  {/* Implementor */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.implementor`}>
                      Implementor <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id={`activities.${index}.implementor`}
                      {...register(`activities.${index}.implementor`, {
                        onBlur: (e) => {
                          onBlur(e)
                          handleActivityUpdate(index, 'implementor', e.target.value)
                        }
                      })}
                      placeholder="Enter implementor name"
                    />
                    {errors.activities?.[index]?.implementor && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.implementor?.message}
                      </p>
                    )}
                  </div>

                  {/* Implementing Unit */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.implementingUnit`}>
                      Implementing Unit <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id={`activities.${index}.implementingUnit`}
                      {...register(`activities.${index}.implementingUnit`, {
                        onBlur: (e) => {
                          onBlur(e)
                          handleActivityUpdate(index, 'implementingUnit', e.target.value)
                        }
                      })}
                      placeholder="Enter implementing unit"
                    />
                    {errors.activities?.[index]?.implementingUnit && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.implementingUnit?.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Fiscal Year and Dates */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Fiscal Year */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.fiscalYear`}>
                      Fiscal Year <span className="text-destructive">*</span>
                    </Label>
                    <Select
                      onValueChange={(value) => {
                        setValue(`activities.${index}.fiscalYear`, value)
                        handleActivityUpdate(index, 'fiscalYear', value)
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select fiscal year" />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredFiscalYears.map((fy) => (
                          <SelectItem key={fy.id} value={fy.year}>
                            {fy.year}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.activities?.[index]?.fiscalYear && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.fiscalYear?.message}
                      </p>
                    )}
                  </div>

                  {/* Start Date */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.startDate`}>
                      Start Date <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id={`activities.${index}.startDate`}
                      type="date"
                      {...register(`activities.${index}.startDate`, {
                        onBlur: (e) => {
                          onBlur(e)
                          handleActivityUpdate(index, 'startDate', e.target.value)
                        }
                      })}
                    />
                    {errors.activities?.[index]?.startDate && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.startDate?.message}
                      </p>
                    )}
                  </div>

                  {/* End Date */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.endDate`}>
                      End Date <span className="text-destructive">*</span>
                    </Label>
                    <Input
                      id={`activities.${index}.endDate`}
                      type="date"
                      {...register(`activities.${index}.endDate`, {
                        onBlur: (e) => {
                          onBlur(e)
                          handleActivityUpdate(index, 'endDate', e.target.value)
                        }
                      })}
                    />
                    {errors.activities?.[index]?.endDate && (
                      <p className="text-sm text-destructive">
                        {errors.activities[index]?.endDate?.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Domain of Intervention - Cascading Dropdowns */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">
                    Domain of Intervention <span className="text-destructive">*</span>
                  </Label>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Domain */}
                    <div className="space-y-2">
                      <Label htmlFor={`activities.${index}.domain`}>Domain</Label>
                      <Select
                        onValueChange={(value) => {
                          setValue(`activities.${index}.domain`, value)
                          setValue(`activities.${index}.subDomain`, '')
                          setValue(`activities.${index}.subDomainFunction`, '')
                          setValue(`activities.${index}.subFunction`, '')
                          handleActivityUpdate(index, 'domain', value)
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select domain" />
                        </SelectTrigger>
                        <SelectContent>
                          {dropdownData.domains.map((domain) => (
                            <SelectItem key={domain.id} value={domain.id}>
                              {domain.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Sub Domain */}
                    <div className="space-y-2">
                      <Label htmlFor={`activities.${index}.subDomain`}>Sub Domain</Label>
                      <Select
                        onValueChange={(value) => {
                          setValue(`activities.${index}.subDomain`, value)
                          setValue(`activities.${index}.subDomainFunction`, '')
                          setValue(`activities.${index}.subFunction`, '')
                          handleActivityUpdate(index, 'subDomain', value)
                        }}
                        disabled={!activities[index]?.domain}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select sub domain" />
                        </SelectTrigger>
                        <SelectContent>
                          {getSubDomains(activities[index]?.domain || '').map((subDomain) => (
                            <SelectItem key={subDomain.id} value={subDomain.id}>
                              {subDomain.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Sub Domain Function */}
                    <div className="space-y-2">
                      <Label htmlFor={`activities.${index}.subDomainFunction`}>Sub Domain Function</Label>
                      <Select
                        onValueChange={(value) => {
                          setValue(`activities.${index}.subDomainFunction`, value)
                          setValue(`activities.${index}.subFunction`, '')
                          handleActivityUpdate(index, 'subDomainFunction', value)
                        }}
                        disabled={!activities[index]?.subDomain}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select function" />
                        </SelectTrigger>
                        <SelectContent>
                          {getDomainFunctions(
                            activities[index]?.domain || '',
                            activities[index]?.subDomain || ''
                          ).map((func) => (
                            <SelectItem key={func.id} value={func.id}>
                              {func.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Sub Function */}
                    <div className="space-y-2">
                      <Label htmlFor={`activities.${index}.subFunction`}>Sub Function</Label>
                      <Select
                        onValueChange={(value) => {
                          setValue(`activities.${index}.subFunction`, value)
                          handleActivityUpdate(index, 'subFunction', value)
                        }}
                        disabled={!activities[index]?.subDomainFunction}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select sub function" />
                        </SelectTrigger>
                        <SelectContent>
                          {getSubFunctions(
                            activities[index]?.domain || '',
                            activities[index]?.subDomain || '',
                            activities[index]?.subDomainFunction || ''
                          ).map((subFunc) => (
                            <SelectItem key={subFunc.id} value={subFunc.id}>
                              {subFunc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Budget Allocation */}
                <div className="space-y-4">
                  <Label className="text-base font-medium">
                    Budget Allocation <span className="text-destructive">*</span>
                  </Label>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Input Category */}
                    <div className="space-y-2">
                      <Label htmlFor={`activities.${index}.inputCategory`}>Category</Label>
                      <Select
                        onValueChange={(value) => {
                          setValue(`activities.${index}.inputCategory`, value)
                          setValue(`activities.${index}.activityInput`, '')
                          handleActivityUpdate(index, 'inputCategory', value)
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {dropdownData.inputCategories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Activity Input */}
                    <div className="space-y-2">
                      <Label htmlFor={`activities.${index}.activityInput`}>Activity Input</Label>
                      <Select
                        onValueChange={(value) => {
                          setValue(`activities.${index}.activityInput`, value)
                          handleActivityUpdate(index, 'activityInput', value)
                        }}
                        disabled={!activities[index]?.inputCategory}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select activity input" />
                        </SelectTrigger>
                        <SelectContent>
                          {getActivityInputs(activities[index]?.inputCategory || '').map((input) => (
                            <SelectItem key={input.id} value={input.id}>
                              {input.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                {/* Location Selection */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    <Label className="text-base font-medium">
                      Location Selection <span className="text-destructive">*</span>
                    </Label>
                  </div>

                  {/* Geographic Level */}
                  <div className="space-y-2">
                    <Label htmlFor={`activities.${index}.geographicLevel`}>Geographic Level</Label>
                    <Select
                      onValueChange={(value) => {
                        setValue(`activities.${index}.geographicLevel`, value as 'Provinces' | 'Central')
                        setValue(`activities.${index}.budgetAllocations`, [])
                        handleActivityUpdate(index, 'geographicLevel', value)
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select geographic level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Provinces">Provinces</SelectItem>
                        <SelectItem value="Central">Central Level</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Budget Allocations based on Geographic Level */}
                  {activities[index]?.geographicLevel && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">Budget Allocations</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentAllocations = activities[index]?.budgetAllocations || []
                            const newAllocation = {
                              location: '',
                              budget: 0
                            }
                            setValue(`activities.${index}.budgetAllocations`, [...currentAllocations, newAllocation])
                          }}
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Add {activities[index]?.geographicLevel === 'Provinces' ? 'Province' : 'Central Level'}
                        </Button>
                      </div>

                      {activities[index]?.budgetAllocations?.map((allocation, allocIndex) => (
                        <div key={allocIndex} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 border rounded-lg">
                          <div className="space-y-2">
                            <Label>
                              {activities[index]?.geographicLevel === 'Provinces' ? 'Province' : 'Central Level'}
                            </Label>
                            <Select
                              onValueChange={(value) => {
                                const currentAllocations = [...(activities[index]?.budgetAllocations || [])]
                                currentAllocations[allocIndex] = { ...currentAllocations[allocIndex], location: value }
                                setValue(`activities.${index}.budgetAllocations`, currentAllocations)
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder={`Select ${activities[index]?.geographicLevel === 'Provinces' ? 'province' : 'central level'}`} />
                              </SelectTrigger>
                              <SelectContent>
                                {activities[index]?.geographicLevel === 'Provinces'
                                  ? dropdownData.provinces.map((province) => (
                                      <SelectItem key={province.id} value={province.id}>
                                        {province.name}
                                      </SelectItem>
                                    ))
                                  : dropdownData.centralLevels.map((level) => (
                                      <SelectItem key={level.id} value={level.id}>
                                        {level.name}
                                      </SelectItem>
                                    ))
                                }
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label>Budget Amount</Label>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              {...register(`activities.${index}.budgetAllocations.${allocIndex}.budget`, {
                                valueAsNumber: true,
                                onBlur: onBlur
                              })}
                              placeholder="Enter budget"
                            />
                          </div>

                          <div className="flex items-end">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const currentAllocations = [...(activities[index]?.budgetAllocations || [])]
                                currentAllocations.splice(allocIndex, 1)
                                setValue(`activities.${index}.budgetAllocations`, currentAllocations)
                              }}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add Activity Button */}
      {availableProjects.length > 0 && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleAddActivity}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Activity
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              Create activities for your projects
            </p>
          </CardContent>
        </Card>
      )}

      {/* Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Note:</strong> Activities represent specific tasks or initiatives within your projects. 
          Ensure budget allocations don't exceed the total project budget.
        </AlertDescription>
      </Alert>
    </div>
  )
}
