import { z } from 'zod';
import { fileUploadConfig } from '@/data/mock-data';

// Step 1: MoU Details validation
export const mouDetailsSchema = z.object({
  organizationName: z.string().min(1, 'Organization name is required'),
  mouDuration: z.number().min(1, 'Duration must be at least 1 year').max(10, 'Duration cannot exceed 10 years'),
  extendedDurationReason: z.string().optional()
}).refine(data => {
  if (data.mouDuration > 1 && (!data.extendedDurationReason || data.extendedDurationReason.trim() === '')) {
    return false;
  }
  return true;
}, {
  message: "Reason is required when duration is more than 1 year",
  path: ["extendedDurationReason"]
});

// Step 2: Party Details validation
export const partySchema = z.object({
  name: z.string().min(1, 'Party name is required').max(100, 'Party name is too long'),
  signatoryName: z.string().min(1, 'Signatory name is required').max(100, 'Signatory name is too long'),
  position: z.string().min(1, 'Position is required').max(100, 'Position is too long'),
  responsibilities: z.string().optional() // Made optional since Party 2+ doesn't need responsibilities
});

export const partiesSchema = z.object({
  parties: z.array(partySchema).min(1, 'At least one party is required').max(10, 'Maximum 10 parties allowed')
});

// Step 3: Projects validation
export const fiscalYearBudgetSchema = z.object({
  fiscalYear: z.string().min(1, 'Fiscal year is required'),
  budget: z.number().min(0, 'Budget must be a positive number').max(1000000000, 'Budget is too large')
});

export const projectGoalSchema = z.object({
  description: z.string().optional(),
  isOverallGoal: z.boolean()
});

export const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required').max(200, 'Project name is too long'),
  fundingSourceId: z.string().min(1, 'Funding source is required'),
  fundingUnitId: z.string().min(1, 'Funding unit is required'),
  budgetTypeId: z.string().min(1, 'Budget type is required'),
  currencyId: z.string().min(1, 'Currency is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  fiscalYearBudgets: z.array(fiscalYearBudgetSchema).min(1, 'At least one fiscal year budget is required'),
  goals: z.array(projectGoalSchema).min(1, 'At least one goal is required')
}).refine(data => {
  const startDate = new Date(data.startDate);
  const endDate = new Date(data.endDate);
  return startDate < endDate;
}, {
  message: "End date must be after start date",
  path: ["endDate"]
}).refine(data => {
  const overallGoals = data.goals.filter(g => g.isOverallGoal);
  return overallGoals.length === 1;
}, {
  message: "Exactly one overall goal is required",
  path: ["goals"]
});

export const projectsSchema = z.object({
  projects: z.array(projectSchema).min(1, 'At least one project is required').max(20, 'Maximum 20 projects allowed')
});

// Step 4: Activities validation
export const budgetAllocationSchema = z.object({
  location: z.string().min(1, 'Location is required'),
  budget: z.number().min(0, 'Budget must be a positive number')
});

export const activitySchema = z.object({
  projectId: z.string().min(1, 'Project ID is required'),
  name: z.string().min(1, 'Activity name is required').max(200, 'Activity name is too long'),
  implementor: z.string().min(1, 'Implementor is required').max(100, 'Implementor name is too long'),
  implementingUnit: z.string().min(1, 'Implementing unit is required').max(100, 'Implementing unit name is too long'),
  fiscalYear: z.string().min(1, 'Fiscal year is required'),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().min(1, 'End date is required'),
  domain: z.string().min(1, 'Domain is required'),
  subDomain: z.string().min(1, 'Sub domain is required'),
  subDomainFunction: z.string().min(1, 'Sub domain function is required'),
  subFunction: z.string().min(1, 'Sub function is required'),
  inputCategory: z.string().min(1, 'Input category is required'),
  activityInput: z.string().min(1, 'Activity input is required'),
  geographicLevel: z.enum(['Provinces', 'Central'], {
    required_error: 'Geographic level is required'
  }),
  budgetAllocations: z.array(budgetAllocationSchema).min(1, 'At least one budget allocation is required')
}).refine(data => {
  const startDate = new Date(data.startDate);
  const endDate = new Date(data.endDate);
  return startDate < endDate;
}, {
  message: "End date must be after start date",
  path: ["endDate"]
});

export const activitiesSchema = z.object({
  activities: z.array(activitySchema)
});

// Step 5: Documents validation
export const documentSchema = z.object({
  file: z.instanceof(File, { message: 'File is required' })
    .refine(file => file.size <= fileUploadConfig.maxSize, {
      message: `File size must be less than ${Math.round(fileUploadConfig.maxSize / (1024 * 1024))}MB`
    })
    .refine(file => {
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      return fileUploadConfig.acceptedExtensions.includes(fileExtension);
    }, {
      message: `File type must be one of: ${fileUploadConfig.acceptedExtensions.join(', ')}`
    })
});

export const documentsSchema = z.object({
  documents: z.object({
    MEMO_OBJECTIVE: documentSchema,
    STRATEGIC_PLAN: documentSchema,
    CAPACITY_BUILDING: documentSchema,
    MEMO_FUNDS: documentSchema
  })
});

// Complete form validation
export const mouApplicationSchema = z.object({
  mouDetails: mouDetailsSchema,
  parties: partiesSchema,
  projects: projectsSchema,
  activities: activitiesSchema,
  documents: documentsSchema
});

// Step validation functions
export const validateStep = (step: number, data: any) => {
  try {
    switch (step) {
      case 1:
        mouDetailsSchema.parse(data);
        return { isValid: true, errors: {} };
      case 2:
        // Validate basic party requirements
        partiesSchema.parse({ parties: data.parties });

        // Additional validation: Check basic required fields for primary party
        if (data.parties && data.parties.length > 0) {
          const primaryParty = data.parties[0];

          // Check if all required basic fields are filled
          if (!primaryParty.name?.trim()) {
            throw new Error("Primary party organization name is required");
          }
          if (!primaryParty.signatoryName?.trim()) {
            throw new Error("Primary party signatory name is required");
          }
          if (!primaryParty.position?.trim()) {
            throw new Error("Primary party position is required");
          }

          // Responsibilities are optional for progression - can be added later
          // This allows users to move forward with basic info and add responsibilities later
        }

        return { isValid: true, errors: {} };
      case 3:
        projectsSchema.parse({ projects: data.projects });
        return { isValid: true, errors: {} };
      case 4:
        activitiesSchema.parse({ activities: data.activities });
        return { isValid: true, errors: {} };
      case 5:
        const documentData = {
          documents: {
            MEMO_OBJECTIVE: data.documents.find((d: any) => d.type === 'MEMO_OBJECTIVE')?.file,
            STRATEGIC_PLAN: data.documents.find((d: any) => d.type === 'STRATEGIC_PLAN')?.file,
            CAPACITY_BUILDING: data.documents.find((d: any) => d.type === 'CAPACITY_BUILDING')?.file,
            MEMO_FUNDS: data.documents.find((d: any) => d.type === 'MEMO_FUNDS')?.file
          }
        };
        documentsSchema.parse(documentData);
        return { isValid: true, errors: {} };
      default:
        return { isValid: false, errors: { general: 'Invalid step' } };
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: any = {};
      error.errors.forEach(err => {
        const path = err.path.join('.');
        errors[path] = err.message;
      });
      return { isValid: false, errors };
    }
    return { isValid: false, errors: { general: 'Validation error' } };
  }
};

// Budget validation helpers
export const validateProjectBudgets = (projects: any[], activities: any[]) => {
  const errors: any = {};
  
  projects.forEach((project, projectIndex) => {
    const projectActivities = activities.filter(a => a.projectId === project.id);
    const totalActivityBudget = projectActivities.reduce((sum, activity) => {
      return sum + activity.budgetAllocations.reduce((actSum: number, allocation: any) => actSum + allocation.budget, 0);
    }, 0);
    
    if (totalActivityBudget > project.totalBudget) {
      errors[`projects.${projectIndex}.budget`] = 'Total activity budgets exceed project budget';
    }
  });
  
  return errors;
};

export type MouDetailsFormData = z.infer<typeof mouDetailsSchema>;
export type PartyFormData = z.infer<typeof partySchema>;
export type ProjectFormData = z.infer<typeof projectSchema>;
export type ActivityFormData = z.infer<typeof activitySchema>;
export type DocumentFormData = z.infer<typeof documentSchema>;
export type MouApplicationFormData = z.infer<typeof mouApplicationSchema>;
