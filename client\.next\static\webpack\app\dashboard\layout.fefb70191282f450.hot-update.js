"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./components/app-sidebar.tsx":
/*!************************************!*\
  !*** ./components/app-sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./contexts/auth-context.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _partner_nav__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./partner-nav */ \"(app-pages-browser)/./components/partner-nav.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Building2,CheckCircle,ChevronRight,Clock,DollarSign,FileText,LogOut,Menu,Settings,Shield,UserCircle,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./components/ui/collapsible.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AppSidebar() {\n    var _user_firstName, _user_lastName;\n    _s();\n    const { user, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [openSections, setOpenSections] = react__WEBPACK_IMPORTED_MODULE_1__.useState([\n        'mou-list',\n        'applications'\n    ]);\n    const isActive = (path)=>{\n        return pathname === path || pathname.startsWith(\"\".concat(path, \"/\"));\n    };\n    const toggleSection = (section)=>{\n        setOpenSections((prev)=>prev.includes(section) ? prev.filter((s)=>s !== section) : [\n                ...prev,\n                section\n            ]);\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'PARTNER') {\n        var _user_firstName1, _user_lastName1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n            className: \"border-r border-gray-200 bg-white shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarHeader, {\n                    className: \"border-b border-gray-100 bg-gradient-to-r from-blue-600 to-indigo-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 px-6 py-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarTrigger, {\n                                className: \"md:hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-6 w-6 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-12 w-12 items-center justify-center rounded-xl bg-white/20 backdrop-blur-sm shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-7 w-7 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl font-bold text-white\",\n                                                children: \"MoH MoU System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-blue-100 font-medium\",\n                                                children: \"Partner Portal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarContent, {\n                    className: \"bg-gray-50 px-4 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_partner_nav__WEBPACK_IMPORTED_MODULE_6__.PartnerNav, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarFooter, {\n                    className: \"border-t border-gray-200 bg-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-100 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__.Avatar, {\n                                    className: \"h-12 w-12 ring-2 ring-blue-100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__.AvatarImage, {\n                                            src: \"/placeholder.svg\",\n                                            alt: user === null || user === void 0 ? void 0 : user.firstName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__.AvatarFallback, {\n                                            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold text-lg\",\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : (_user_firstName1 = user.firstName) === null || _user_firstName1 === void 0 ? void 0 : _user_firstName1[0],\n                                                user === null || user === void 0 ? void 0 : (_user_lastName1 = user.lastName) === null || _user_lastName1 === void 0 ? void 0 : _user_lastName1[0]\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-base font-bold text-gray-900 truncate\",\n                                                    children: [\n                                                        user === null || user === void 0 ? void 0 : user.firstName,\n                                                        \" \",\n                                                        user === null || user === void 0 ? void 0 : user.lastName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                    variant: \"secondary\",\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-xs px-2 py-1 font-semibold\", (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' ? \"bg-red-100 text-red-700\" : \"bg-blue-100 text-blue-700\"),\n                                                    children: user === null || user === void 0 ? void 0 : user.role\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 truncate font-medium\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: ()=>logout(),\n                                    className: \"rounded-xl p-2 hover:bg-red-50 hover:text-red-600 transition-colors h-10 w-10\",\n                                    title: \"Logout\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.Sidebar, {\n        className: \"border-r border-gray-200 bg-white shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarHeader, {\n                className: \"border-b border-gray-100 bg-gradient-to-r from-blue-600 to-indigo-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 px-6 py-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarTrigger, {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex h-12 w-12 items-center justify-center rounded-xl bg-white/20 backdrop-blur-sm shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-7 w-7 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-bold text-white\",\n                                            children: \"MoH MoU System\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-100 font-medium\",\n                                            children: \"Admin Portal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarContent, {\n                className: \"bg-gray-50 px-4 py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                            href: \"/dashboard/admin\",\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg\", isActive(\"/dashboard/admin\") ? \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg\" : \"bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex h-12 w-12 items-center justify-center rounded-xl transition-colors\", isActive(\"/dashboard/admin\") ? \"bg-white/20\" : \"bg-blue-100\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-6 w-6 transition-colors\", isActive(\"/dashboard/admin\") ? \"text-white\" : \"text-blue-600\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-bold text-base truncate\", isActive(\"/dashboard/admin\") ? \"text-white\" : \"text-gray-900\"),\n                                                children: \"Admin Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-sm truncate font-medium\", isActive(\"/dashboard/admin\") ? \"text-blue-100\" : \"text-gray-500\"),\n                                            children: \"Overview, Analytics & Reports\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mb-4 flex items-center gap-3 px-3 text-sm font-bold uppercase tracking-wider text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-6 w-6 items-center justify-center rounded-md bg-green-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"MoU Management\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.Collapsible, {\n                                        open: openSections.includes('mou-list'),\n                                        onOpenChange: ()=>toggleSection('mou-list'),\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-12 w-12 items-center justify-center rounded-xl bg-green-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-green-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-base\",\n                                                                        children: \"Active MoUs\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 font-medium\",\n                                                                        children: \"Manage active agreements\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"bg-green-100 text-green-700 text-xs px-2 py-1 ml-auto mr-2 font-semibold\",\n                                                                children: \"45\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform text-gray-400\", openSections.includes('mou-list') && \"rotate-90\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 space-y-1 pl-16\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/mou/active\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/mou/active\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"View Active MoUs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/mou/expired\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/mou/expired\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"View Expired MoUs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.Collapsible, {\n                                        open: openSections.includes('applications'),\n                                        onOpenChange: ()=>toggleSection('applications'),\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-12 w-12 items-center justify-center rounded-xl bg-amber-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-amber-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-base\",\n                                                                        children: \"Applications\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 font-medium\",\n                                                                        children: \"Review & approve\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"bg-amber-100 text-amber-700 text-xs px-2 py-1 ml-auto mr-2 font-semibold\",\n                                                                children: \"12\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform text-gray-400\", openSections.includes('applications') && \"rotate-90\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 space-y-1 pl-16\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/applications/pending\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/applications/pending\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Pending Review\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 278,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-amber-50 text-amber-600 border-amber-200 text-xs\",\n                                                                    children: \"8\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/applications/review\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/applications/review\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"In Review\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-blue-50 text-blue-600 border-blue-200 text-xs\",\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 291,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/applications/draft\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/applications/draft\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Draft Applications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/applications/rejected\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/applications/rejected\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Rejected Applications\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mb-4 flex items-center gap-3 px-3 text-sm font-bold uppercase tracking-wider text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-6 w-6 items-center justify-center rounded-md bg-purple-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"System Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.Collapsible, {\n                                        open: openSections.includes('organization'),\n                                        onOpenChange: ()=>toggleSection('organization'),\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-12 w-12 items-center justify-center rounded-xl bg-purple-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-purple-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 338,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-base\",\n                                                                        children: \"Organizations\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 font-medium\",\n                                                                        children: \"Setup & types\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 343,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform text-gray-400\", openSections.includes('organization') && \"rotate-90\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 space-y-1 pl-16\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/domain-interventions\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/domain-interventions\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Domain Interventions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/organization-types\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/organization-types\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Organization Types\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/healthcare-providers\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/healthcare-providers\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Healthcare Providers\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/input-categories\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/input-categories\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Input Categories\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.Collapsible, {\n                                        open: openSections.includes('financial'),\n                                        onOpenChange: ()=>toggleSection('financial'),\n                                        className: \"w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleTrigger, {\n                                                className: \"flex w-full items-center justify-between rounded-xl px-4 py-3 text-sm font-medium text-gray-700 hover:bg-white hover:shadow-md transition-all duration-200 bg-white/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex h-12 w-12 items-center justify-center rounded-xl bg-emerald-100\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-emerald-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-base\",\n                                                                        children: \"Financial Setup\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 413,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-gray-500 font-medium\",\n                                                                        children: \"Budget & funding\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-4 w-4 transition-transform text-gray-400\", openSections.includes('financial') && \"rotate-90\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_11__.CollapsibleContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-2 space-y-1 pl-16\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/budget-types\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/budget-types\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Budget Types\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/funding-source\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/funding-source\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Funding Sources\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/funding-units\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/funding-units\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Funding Units\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/financing-schemes\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/financing-schemes\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Financing Schemes\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/financing-agents\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/financing-agents\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Financing Agents\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: \"/dashboard/currency\",\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex w-full items-center rounded-lg px-3 py-2 text-sm transition-colors\", isActive(\"/dashboard/currency\") ? \"bg-blue-100 text-blue-700 font-medium\" : \"text-gray-600 hover:bg-gray-100 hover:text-gray-700\"),\n                                                            children: \"Currencies\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"mb-4 flex items-center gap-3 px-3 text-sm font-bold uppercase tracking-wider text-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex h-6 w-6 items-center justify-center rounded-md bg-indigo-100\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4 text-indigo-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"User Management\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/dashboard/users\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg\", isActive(\"/dashboard/users\") ? \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg\" : \"bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex h-12 w-12 items-center justify-center rounded-xl transition-colors\", isActive(\"/dashboard/users\") ? \"bg-white/20\" : \"bg-indigo-100\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-6 w-6 transition-colors\", isActive(\"/dashboard/users\") ? \"text-white\" : \"text-indigo-600\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-bold text-base truncate\", isActive(\"/dashboard/users\") ? \"text-white\" : \"text-gray-900\"),\n                                                            children: \"Manage Users\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-sm truncate font-medium\", isActive(\"/dashboard/users\") ? \"text-blue-100\" : \"text-gray-500\"),\n                                                        children: \"User accounts & permissions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                        href: \"/dashboard/profile\",\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex items-center gap-4 rounded-xl p-4 transition-all duration-200 hover:shadow-lg\", isActive(\"/dashboard/profile\") ? \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg\" : \"bg-white text-gray-700 hover:bg-gray-50 border border-gray-100 hover:border-gray-200\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex h-12 w-12 items-center justify-center rounded-xl transition-colors\", isActive(\"/dashboard/profile\") ? \"bg-white/20\" : \"bg-gray-100\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"h-6 w-6 transition-colors\", isActive(\"/dashboard/profile\") ? \"text-white\" : \"text-gray-600\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 mb-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"font-bold text-base truncate\", isActive(\"/dashboard/profile\") ? \"text-white\" : \"text-gray-900\"),\n                                                            children: \"My Profile\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-sm truncate font-medium\", isActive(\"/dashboard/profile\") ? \"text-blue-100\" : \"text-gray-500\"),\n                                                        children: \"Personal settings & info\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_7__.SidebarFooter, {\n                className: \"border-t border-gray-200 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-100 shadow-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__.Avatar, {\n                                className: \"h-12 w-12 ring-2 ring-blue-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__.AvatarImage, {\n                                        src: \"/placeholder.svg\",\n                                        alt: user === null || user === void 0 ? void 0 : user.firstName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_8__.AvatarFallback, {\n                                        className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-bold text-lg\",\n                                        children: [\n                                            user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName[0],\n                                            user === null || user === void 0 ? void 0 : (_user_lastName = user.lastName) === null || _user_lastName === void 0 ? void 0 : _user_lastName[0]\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 583,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-base font-bold text-gray-900 truncate\",\n                                                children: [\n                                                    user === null || user === void 0 ? void 0 : user.firstName,\n                                                    \" \",\n                                                    user === null || user === void 0 ? void 0 : user.lastName\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_10__.Badge, {\n                                                variant: \"secondary\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"text-xs px-2 py-1 font-semibold\", (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' ? \"bg-red-100 text-red-700\" : \"bg-blue-100 text-blue-700\"),\n                                                children: user === null || user === void 0 ? void 0 : user.role\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 truncate font-medium\",\n                                        children: user === null || user === void 0 ? void 0 : user.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_9__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: ()=>logout(),\n                                className: \"rounded-xl p-2 hover:bg-red-50 hover:text-red-600 transition-colors h-10 w-10\",\n                                title: \"Logout\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Building2_CheckCircle_ChevronRight_Clock_DollarSign_FileText_LogOut_Menu_Settings_Shield_UserCircle_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 582,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 581,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 580,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\MoU Application_zip\\\\MoU Application\\\\client\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 125,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"Fh4GlFfrbOjpl26hZuBbmIa/jHs=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/app-sidebar.tsx\n"));

/***/ })

});