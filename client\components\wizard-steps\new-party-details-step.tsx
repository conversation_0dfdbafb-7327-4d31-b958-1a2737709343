"use client"

import { useCallback } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldArray, useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useFieldBlurAutoSave } from '@/hooks/use-auto-save'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { partiesSchema } from '@/lib/validations/mou-application'
import { Trash2, Plus, Users, AlertTriangle, Info } from 'lucide-react'

type FormData = z.infer<typeof partiesSchema>

export function NewPartyDetailsStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const { data, addParty, removeParty, updateParty } = useMouApplicationStore()

  const {
    control,
    register,
    handleSubmit,
    watch,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(partiesSchema),
    defaultValues: {
      parties: data.parties.length > 0 ? data.parties : []
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: "parties"
  })

  const parties = watch('parties')

  // Add new party
  const handleAddParty = useCallback(() => {
    if (fields.length < 2) {
      const newParty = {
        name: '',
        signatoryName: '',
        position: '',
        responsibilities: ''
      }
      append(newParty)
      addParty(newParty)
    }
  }, [fields.length, append, addParty])

  // Remove party
  const handleRemoveParty = useCallback((index: number) => {
    const partyId = data.parties[index]?.id
    if (partyId) {
      removeParty(partyId)
    }
    remove(index)
  }, [remove, removeParty, data.parties])

  // Update party in store when form changes
  const handlePartyUpdate = useCallback((index: number, field: string, value: string) => {
    const partyId = data.parties[index]?.id
    if (partyId) {
      updateParty(partyId, { [field]: value })
    }
  }, [updateParty, data.parties])

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Party Details
          </CardTitle>
          <CardDescription>
            Add signatory parties for this MoU (maximum 2 parties allowed)
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Parties List */}
      <div className="space-y-4">
        {fields.map((field, index) => (
          <Card key={field.id} className="relative">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  Party {index + 1}
                </CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleRemoveParty(index)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                  Remove
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Party Name */}
              <div className="space-y-2">
                <Label htmlFor={`parties.${index}.name`}>
                  Party Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id={`parties.${index}.name`}
                  {...register(`parties.${index}.name`, {
                    onBlur: (e) => {
                      onBlur(e)
                      handlePartyUpdate(index, 'name', e.target.value)
                    }
                  })}
                  placeholder="Enter party/organization name"
                />
                {errors.parties?.[index]?.name && (
                  <p className="text-sm text-destructive">
                    {errors.parties[index]?.name?.message}
                  </p>
                )}
              </div>

              {/* Signatory Name */}
              <div className="space-y-2">
                <Label htmlFor={`parties.${index}.signatoryName`}>
                  Signatory Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id={`parties.${index}.signatoryName`}
                  {...register(`parties.${index}.signatoryName`, {
                    onBlur: (e) => {
                      onBlur(e)
                      handlePartyUpdate(index, 'signatoryName', e.target.value)
                    }
                  })}
                  placeholder="Enter signatory's full name"
                />
                {errors.parties?.[index]?.signatoryName && (
                  <p className="text-sm text-destructive">
                    {errors.parties[index]?.signatoryName?.message}
                  </p>
                )}
              </div>

              {/* Signatory Position */}
              <div className="space-y-2">
                <Label htmlFor={`parties.${index}.position`}>
                  Signatory Position <span className="text-destructive">*</span>
                </Label>
                <Input
                  id={`parties.${index}.position`}
                  {...register(`parties.${index}.position`, {
                    onBlur: (e) => {
                      onBlur(e)
                      handlePartyUpdate(index, 'position', e.target.value)
                    }
                  })}
                  placeholder="Enter signatory's position/title"
                />
                {errors.parties?.[index]?.position && (
                  <p className="text-sm text-destructive">
                    {errors.parties[index]?.position?.message}
                  </p>
                )}
              </div>

              {/* Party Responsibilities */}
              <div className="space-y-2">
                <Label htmlFor={`parties.${index}.responsibilities`}>
                  Party Responsibilities <span className="text-destructive">*</span>
                </Label>
                <Textarea
                  id={`parties.${index}.responsibilities`}
                  {...register(`parties.${index}.responsibilities`, {
                    onBlur: (e) => {
                      onBlur(e)
                      handlePartyUpdate(index, 'responsibilities', e.target.value)
                    }
                  })}
                  placeholder="Describe the responsibilities and obligations of this party..."
                  rows={4}
                  className="resize-none"
                />
                {errors.parties?.[index]?.responsibilities && (
                  <p className="text-sm text-destructive">
                    {errors.parties[index]?.responsibilities?.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add Party Button */}
      {fields.length < 2 && (
        <Card className="border-dashed">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleAddParty}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Party {fields.length === 0 ? '' : `(${2 - fields.length} remaining)`}
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              You can add up to 2 parties for this MoU
            </p>
          </CardContent>
        </Card>
      )}

      {/* Validation Alert */}
      {fields.length === 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            At least one party must be added to proceed to the next step.
          </AlertDescription>
        </Alert>
      )}

      {/* Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Note:</strong> Each party represents a signatory organization in the MoU. 
          Ensure all information is accurate as it will appear in the final agreement.
        </AlertDescription>
      </Alert>
    </div>
  )
}
