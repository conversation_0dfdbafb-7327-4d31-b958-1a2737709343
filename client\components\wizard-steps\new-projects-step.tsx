"use client"

import { use<PERSON><PERSON><PERSON>, useEffect, useState } from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import { useFieldArray, useForm } from 'react-hook-form'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useFieldBlurAutoSave } from '@/hooks/use-auto-save'
import { useMouApplicationStore } from '@/store/mou-application-store'
import { projectsSchema } from '@/lib/validations/mou-application'
import { useDropdownData } from '@/hooks/use-dropdown-data'
import { Trash2, Plus, <PERSON>olderO<PERSON>, Alert<PERSON>riangle, <PERSON><PERSON>, Cal<PERSON><PERSON>, Loader2 } from 'lucide-react'

type FormData = z.infer<typeof projectsSchema>

export function NewProjectsStep() {
  const { onBlur } = useFieldBlurAutoSave()
  const { data, addProject, removeProject, updateProject } = useMouApplicationStore()
  const { data: dropdownData, loading: dropdownLoading, error: dropdownError } = useDropdownData()

  const {
    control,
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FormData>({
    resolver: zodResolver(projectsSchema),
    defaultValues: {
      projects: data.projects.length > 0 ? data.projects : []
    }
  })

  const { fields, append, remove } = useFieldArray({
    control,
    name: "projects"
  })

  const projects = watch('projects')

  // Add new project
  const handleAddProject = useCallback(() => {
    const newProject = {
      name: '',
      fundingSourceId: '',
      fundingUnitId: '',
      budgetTypeId: '',
      currencyId: '',
      startDate: '',
      endDate: '',
      fiscalYearBudgets: [
        {
          fiscalYear: '2024-2025',
          budget: 1000
        }
      ],
      goals: [
        {
          description: '',
          isOverallGoal: true
        }
      ]
    }
    append(newProject)
    addProject(newProject)
  }, [append, addProject])

  // Remove project
  const handleRemoveProject = useCallback((index: number) => {
    const projectId = data.projects[index]?.id
    if (projectId) {
      removeProject(projectId)
    }
    remove(index)
  }, [remove, removeProject, data.projects])

  // Calculate total budget for a project
  const calculateTotalBudget = useCallback((projectIndex: number) => {
    const project = projects[projectIndex]
    if (!project?.fiscalYearBudgets) return 0
    return project.fiscalYearBudgets.reduce((sum, fy) => sum + (fy.budget || 0), 0)
  }, [projects])

  // Update project in store when form changes
  const handleProjectUpdate = useCallback((index: number, field: string, value: any) => {
    const projectId = data.projects[index]?.id
    if (projectId) {
      updateProject(projectId, { [field]: value })
    }
  }, [updateProject, data.projects])

  // Show loading state while fetching dropdown data
  if (dropdownLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading project data...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Show error state if dropdown data failed to load
  if (dropdownError) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load project configuration data. Please refresh the page and try again.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Project Information
          </CardTitle>
          <CardDescription>
            Define projects that will be covered under this MoU
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Projects List */}
      <div className="space-y-6">
        {fields.map((field, index) => (
          <Card key={field.id} className="relative">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">
                  Project {index + 1}
                </CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleRemoveProject(index)}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4" />
                  Remove
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Project Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Project Name */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor={`projects.${index}.name`}>
                    Project Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={`projects.${index}.name`}
                    {...register(`projects.${index}.name`, {
                      onBlur: (e) => {
                        onBlur(e)
                        handleProjectUpdate(index, 'name', e.target.value)
                      }
                    })}
                    placeholder="Enter project name"
                  />
                  {errors.projects?.[index]?.name && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.name?.message}
                    </p>
                  )}
                </div>

                {/* Funding Source */}
                <div className="space-y-2">
                  <Label htmlFor={`projects.${index}.fundingSourceId`}>
                    Funding Source <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    onValueChange={(value) => {
                      setValue(`projects.${index}.fundingSourceId`, value)
                      handleProjectUpdate(index, 'fundingSourceId', value)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select funding source" />
                    </SelectTrigger>
                    <SelectContent>
                      {dropdownData.fundingSources.map((source) => (
                        <SelectItem key={source.id} value={source.id}>
                          {source.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.projects?.[index]?.fundingSourceId && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.fundingSourceId?.message}
                    </p>
                  )}
                </div>

                {/* Funding Unit */}
                <div className="space-y-2">
                  <Label htmlFor={`projects.${index}.fundingUnitId`}>
                    Funding Unit <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    onValueChange={(value) => {
                      setValue(`projects.${index}.fundingUnitId`, value)
                      handleProjectUpdate(index, 'fundingUnitId', value)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select funding unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {dropdownData.fundingUnits.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          {unit.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.projects?.[index]?.fundingUnitId && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.fundingUnitId?.message}
                    </p>
                  )}
                </div>

                {/* Budget Type */}
                <div className="space-y-2">
                  <Label htmlFor={`projects.${index}.budgetTypeId`}>
                    Budget Type <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    onValueChange={(value) => {
                      setValue(`projects.${index}.budgetTypeId`, value)
                      handleProjectUpdate(index, 'budgetTypeId', value)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select budget type" />
                    </SelectTrigger>
                    <SelectContent>
                      {dropdownData.budgetTypes.map((type) => (
                        <SelectItem key={type.id} value={type.id}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.projects?.[index]?.budgetTypeId && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.budgetTypeId?.message}
                    </p>
                  )}
                </div>

                {/* Currency */}
                <div className="space-y-2">
                  <Label htmlFor={`projects.${index}.currencyId`}>
                    Currency <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    onValueChange={(value) => {
                      setValue(`projects.${index}.currencyId`, value)
                      handleProjectUpdate(index, 'currencyId', value)
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      {dropdownData.currencies.map((currency) => (
                        <SelectItem key={currency.id} value={currency.id}>
                          {currency.code} - {currency.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.projects?.[index]?.currencyId && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.currencyId?.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Project Duration */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`projects.${index}.startDate`}>
                    Start Date <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={`projects.${index}.startDate`}
                    type="date"
                    {...register(`projects.${index}.startDate`, {
                      onBlur: (e) => {
                        onBlur(e)
                        handleProjectUpdate(index, 'startDate', e.target.value)
                      }
                    })}
                  />
                  {errors.projects?.[index]?.startDate && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.startDate?.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`projects.${index}.endDate`}>
                    End Date <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={`projects.${index}.endDate`}
                    type="date"
                    {...register(`projects.${index}.endDate`, {
                      onBlur: (e) => {
                        onBlur(e)
                        handleProjectUpdate(index, 'endDate', e.target.value)
                      }
                    })}
                  />
                  {errors.projects?.[index]?.endDate && (
                    <p className="text-sm text-destructive">
                      {errors.projects[index]?.endDate?.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Fiscal Year Budgets */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    Fiscal Year Budgets <span className="text-destructive">*</span>
                  </Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const currentBudgets = projects[index]?.fiscalYearBudgets || []
                      const newBudget = {
                        fiscalYear: '2025-2026',
                        budget: 1000
                      }
                      setValue(`projects.${index}.fiscalYearBudgets`, [...currentBudgets, newBudget])
                    }}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Fiscal Year
                  </Button>
                </div>

                {projects[index]?.fiscalYearBudgets?.map((fyBudget, fyIndex) => (
                  <div key={fyIndex} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border rounded-lg">
                    <div className="space-y-2">
                      <Label>Fiscal Year</Label>
                      <Select
                        onValueChange={(value) => {
                          const currentBudgets = [...(projects[index]?.fiscalYearBudgets || [])]
                          currentBudgets[fyIndex] = { ...currentBudgets[fyIndex], fiscalYear: value }
                          setValue(`projects.${index}.fiscalYearBudgets`, currentBudgets)
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select fiscal year" />
                        </SelectTrigger>
                        <SelectContent>
                          {dropdownData.fiscalYears.map((fy) => (
                            <SelectItem key={fy.id} value={fy.year}>
                              {fy.year}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Budget Amount</Label>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        {...register(`projects.${index}.fiscalYearBudgets.${fyIndex}.budget`, {
                          valueAsNumber: true,
                          onBlur: onBlur
                        })}
                        placeholder="Enter budget amount (e.g., 10000)"
                      />
                    </div>

                    <div className="flex items-end">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const currentBudgets = [...(projects[index]?.fiscalYearBudgets || [])]
                          currentBudgets.splice(fyIndex, 1)
                          setValue(`projects.${index}.fiscalYearBudgets`, currentBudgets)
                        }}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Project Goals */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    Project Goals <span className="text-destructive">*</span>
                  </Label>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const currentGoals = projects[index]?.goals || []
                      const newGoal = {
                        description: '',
                        isOverallGoal: false
                      }
                      setValue(`projects.${index}.goals`, [...currentGoals, newGoal])
                    }}
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Add Goal
                  </Button>
                </div>

                {projects[index]?.goals?.map((goal, goalIndex) => (
                  <div key={goalIndex} className="p-4 border rounded-lg space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">
                        {goal.isOverallGoal ? 'Overall Goal' : `Additional Goal ${goalIndex}`}
                      </Label>
                      {!goal.isOverallGoal && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const currentGoals = [...(projects[index]?.goals || [])]
                            currentGoals.splice(goalIndex, 1)
                            setValue(`projects.${index}.goals`, currentGoals)
                          }}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                    <Input
                      {...register(`projects.${index}.goals.${goalIndex}.description`, {
                        onBlur: onBlur
                      })}
                      placeholder={goal.isOverallGoal ? "Enter the overall goal for this project..." : "Enter additional goal..."}
                    />
                  </div>
                ))}
              </div>

              {/* Total Budget Display */}
              <div className="bg-muted p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Calculator className="h-4 w-4" />
                  <span className="font-medium">Total Project Budget</span>
                </div>
                <p className="text-2xl font-bold text-primary">
                  {calculateTotalBudget(index).toLocaleString()}
                  {projects[index]?.currencyId && (
                    <span className="text-sm font-normal text-muted-foreground ml-2">
                      {dropdownData.currencies.find(c => c.id === projects[index]?.currencyId)?.code}
                    </span>
                  )}
                </p>
                <p className="text-sm text-muted-foreground">
                  Auto-calculated from fiscal year budgets above
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add Project Button */}
      <Card className="border-dashed">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Button
            type="button"
            variant="outline"
            onClick={handleAddProject}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Project
          </Button>
          <p className="text-sm text-muted-foreground mt-2">
            Add multiple projects that will be covered under this MoU
          </p>
        </CardContent>
      </Card>

      {/* Validation Alert */}
      {fields.length === 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            At least one project must be added to proceed to the next step.
          </AlertDescription>
        </Alert>
      )}

      {/* Information */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Note:</strong> Each project should represent a distinct initiative or program 
          that will be implemented under this MoU. Ensure all financial and timeline information is accurate.
        </AlertDescription>
      </Alert>
    </div>
  )
}
